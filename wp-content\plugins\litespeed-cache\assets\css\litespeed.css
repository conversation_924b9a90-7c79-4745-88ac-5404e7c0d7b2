@font-face {
  font-family: "litespeedfont";
  src: url(data:application/font-woff;base64,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);
	font-weight: normal;
	font-style: normal;
}

#adminmenu #toplevel_page_lscache-settings .menu-icon-generic div.wp-menu-image:before,
#adminmenu #toplevel_page_litespeed .menu-icon-generic div.wp-menu-image:before,
.litespeed-top-toolbar .ab-icon::before {
	content: '\e900';
	font-family: 'litespeedfont' !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#wpadminbar .litespeed-top-toolbar .ab-icon.icon_disabled::before {
	color: #D9534F;
}

*[litespeed-accesskey]:before {
	content: '[' attr(litespeed-accesskey) '] ';
}

/* =======================================
   		  UTILITIES - toggle UI
======================================= */

input[type='checkbox'].litespeed-tiny-toggle {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;

	-webkit-tap-highlight-color: transparent;

	width: auto;
	height: auto;
	vertical-align: middle;
	position: relative;
	border: 0;
	outline: 0;
	cursor: pointer;
	margin: 0 4px;
	background: none;
	box-shadow: none;
}

input[type='checkbox'].litespeed-tiny-toggle:focus {
	box-shadow: none;
}

input[type='checkbox'].litespeed-tiny-toggle:after {
	content: '';
	font-size: 8px;
	font-weight: 400;
	line-height: 18px;
	text-indent: -14px;
	color: #ffffff;
	width: 36px;
	height: 18px;
	display: inline-block;
	background-color: #a7aaad;
	border-radius: 72px;
	box-shadow: 0 0 12px rgb(0 0 0 / 15%) inset;
}

input[type='checkbox'].litespeed-tiny-toggle:before {
	content: '';
	width: 14px;
	height: 14px;
	display: block;
	position: absolute;
	top: 2px;
	left: 2px;
	margin: 0;
	border-radius: 50%;
	background-color: #ffffff;
}

input[type='checkbox'].litespeed-tiny-toggle:checked:before {
	left: 20px;
	margin: 0;
	background-color: #ffffff;
}

input[type='checkbox'].litespeed-tiny-toggle,
input[type='checkbox'].litespeed-tiny-toggle:before,
input[type='checkbox'].litespeed-tiny-toggle:after,
input[type='checkbox'].litespeed-tiny-toggle:checked:before,
input[type='checkbox'].litespeed-tiny-toggle:checked:after {
	transition: ease 0.15s;
}

input[type='checkbox'].litespeed-tiny-toggle:checked:after {
	/*content: 'ON';*/
	background-color: #2271b1;
}

.block-editor__container input[type='checkbox'].litespeed-tiny-toggle {
	border: 0 !important;
}

.block-editor__container input[type='checkbox'].litespeed-tiny-toggle:before {
	top: 5px;
	left: 7px;
}

.block-editor__container input[type='checkbox'].litespeed-tiny-toggle:checked:before {
	left: 23px;
}

/* =======================================
   		  UTILITIES - structure
======================================= */

.litespeed_icon:before {
	/* content: "\e900";
    font-family: 'litespeedfont' !important; */
	content: '';
	background-image: url('../img/lscwp_grayscale_font-icon_22px.svg');
	/* filter: grayscale(1); */
	background-size: 22px;
	background-repeat: no-repeat;
	width: 22px;
	height: 22px;
	vertical-align: middle;
	display: inline-block;
	position: absolute;
	left: 5px;
	top: 8px;
}

.rtl .litespeed_icon:before {
	left: initial;
	right: 5px;
}

.litespeed_icon {
	padding-left: 30px !important;
	position: relative;
}

.rtl .litespeed_icon {
	padding-right: 40px;
}

.litespeed-quic-icon {
	background-image: url('../img/quic-cloud-icon-16x16.svg');
	background-repeat: no-repeat;
	width: 16px;
	height: 16px;
	vertical-align: middle;
	display: inline-block;
}

.litespeed-row {
	margin-top: 5px;
}

.litespeed-reset {
	width: initial;
}

.litespeed-inline {
	display: inline-block;
}

.litespeed-flex {
	display: flex;
}

.litespeed-flex-container {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	height: auto;
}

.litespeed-flex-align-center {
	align-items: center;
}

.litespeed-flex-container > * {
	box-sizing: border-box;
}

.litespeed-flex-container--reverse {
	flex-direction: row-reverse;
}

.litespeed-flex-container .litespeed-icon-vertical-middle {
	margin-left: 0;
}

.litespeed-row-flex {
	display: inline-flex;
}

.litespeed-flex-wrap {
	flex-wrap: wrap;
}

.litespeed-align-right {
	margin-left: auto !important;
}

.litespeed-width-1-2 {
	width: 45%;
	padding: 20px;
}

.litespeed-width-1-3 {
	width: 30%;
	padding: 25px;
}

.litespeed-width-7-10 {
	width: 65%;
	padding: 20px;
}

.litespeed-width-3-10 {
	width: 35%;
	padding: 20px;
}

@media screen and (max-width: 814px) {
	.litespeed-width-7-10 {
		width: 100%;
	}

	.litespeed-width-3-10 {
		width: 100%;
		padding: 0;
	}
}

.litespeed-hide {
	display: none !important;
}

.litespeed-right {
	float: right !important;
}

.litespeed-relative {
	position: relative;
}

.litespeed-align-center {
	margin-left: auto;
	margin-right: auto;
}

/* =======================================
   		  UTILITIES - spacing
======================================= */

.litespeed-left10 {
	margin-left: 10px !important;
}

.litespeed-left20 {
	margin-left: 20px !important;
}

.litespeed-right10 {
	margin-right: 10px !important;
}

.litespeed-right20 {
	margin-right: 20px !important;
}

.litespeed-right30 {
	margin-right: 30px !important;
}

.litespeed-right50 {
	margin-right: 50px !important;
}

.litespeed-top10 {
	margin-top: 10px !important;
}

.litespeed-top15 {
	margin-top: 15px !important;
}

.litespeed-top20 {
	margin-top: 20px !important;
}

.litespeed-top30 {
	margin-top: 30px !important;
}

.litespeed-margin-y5 {
	margin-top: 5px !important;
	margin-bottom: 5px !important;
}

.litespeed-margin-x5 {
	margin-left: 5px !important;
	margin-right: 5px !important;
}

.litespeed-wrap .litespeed-left20,
.litespeed-left20 {
	margin-left: 20px;
}

.litespeed-wrap .litespeed-bg-quic-cloud {
	background: linear-gradient(rgba(230, 242, 242, 1) 10%, rgba(250, 255, 255, 1) 30%);
}

.litespeed-left50 {
	margin-left: 50px;
}

.litespeed-padding-space {
	padding: 5px 10px;
}

.litespeed-margin-bottom10 {
	margin-bottom: 10px !important;
}

.litespeed-margin-bottom20 {
	margin-bottom: 20px !important;
}

.litespeed-margin-bottom-remove {
	margin-bottom: 0px !important;
}

.litespeed-margin-top-remove {
	margin-top: 0px !important;
}

.litespeed-margin-left-remove {
	margin-left: 0px !important;
}

.litespeed-margin-y-remove {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}

.litespeed-empty-space-xlarge {
	margin-top: 8em;
}

.litespeed-empty-space-large {
	margin-top: 6em;
}

.litespeed-empty-space-medium {
	margin-top: 3em;
}

.litespeed-empty-space-small {
	margin-top: 2em;
}

.litespeed-empty-space-tiny {
	margin-top: 1em;
}

/* =======================================
   		UTILITIES - typography
======================================= */

.litespeed-text-jumbo {
	font-size: 3em !important;
}

.litespeed-text-large {
	font-size: 0.75em !important;
}

.litespeed-text-md {
	font-size: 1.2em;
}

.litespeed-text-right {
	text-align: right;
}

.litespeed-text-center {
	text-align: center;
}

.litespeed-text-bold, .litespeed-bold {
	font-weight: 600;
}

/* =======================================
	  			COLORS
======================================= */

.litespeed-default {
	color: #a7a7a7 !important;
}

.litespeed-primary {
	color: #3366cc !important;
}

.litespeed-info {
	color: #3fbfbf !important;
}

.litespeed-success {
	color: #73b38d !important;
}

.litespeed-warning {
	color: #ff8c00 !important;
}

.litespeed-danger {
	color: #dc3545 !important;
}

a.litespeed-danger:hover,
button.litespeed-danger:hover {
	color: #a00 !important;
}

.litespeed-text-success {
	color: #34b15d;
}

.litespeed-form-action {
	color: #1a9292 !important;
}

a.litespeed-form-action:hover,
button.litespeed-form-action:hover {
	color: #36b0af !important;
}

.litespeed-bg-default {
	background-color: #a7a7a7 !important;
}

.litespeed-bg-primary {
	background-color: #3366cc !important;
}

.litespeed-bg-info {
	background-color: #d1ecf1 !important;
}

.litespeed-bg-success {
	background-color: #73b38d !important;
}

.litespeed-bg-warning {
	background-color: #ff8c00 !important;
}

.litespeed-bg-danger {
	background-color: #dc3545 !important;
}

.litespeed-bg-text-success {
	background-color: #34b15d;
}

/* =======================================
	  			LAYOUT
======================================= */

.litespeed-wrap {
	margin: 10px 20px 0 2px;
}

@media screen and (max-width: 600px) {
	.litespeed-wrap h2 .nav-tab {
		border-bottom: 1px solid #c3c4c7;
		margin: 10px 10px 0 0;
	}

	.litespeed-wrap .nav-tab-wrapper {
		margin-bottom: 15px;
	}

	.litespeed-desc a,
	.litespeed-body p > a:not(.button) {
		word-break: break-word;
	}
}

.litespeed-wrap .nav-tab {
	border-bottom-color: inherit;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	margin: 11px 10px -1px 0;
}

.litespeed-wrap .nav-tab-active {
	background: #fff;
	border-bottom-color: #fff;
}

.litespeed-wrap .nav-tab:focus:not(.nav-tab-active),
.litespeed-wrap .nav-tab:hover:not(.nav-tab-active) {
	background-color: #f1f1f1;
	color: #444;
}

.litespeed-body {
	background: #fff;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	padding: 1px 20px 20px 20px;
}

@media screen and (min-width: 681px) {
	.litespeed-header + .litespeed-body {
		border-top: none;
	}
}

.litespeed-body table {
	border-collapse: collapse;
	width: 100%;
}

.litespeed-body .litespeed-width-auto {
	width: auto;
}

/* outside stripped table */
.litespeed-description {
	color: #666;
	font-size: 13px;
	margin: 1.5rem 0;
	max-width: 960px;
}

.litespeed-desc-wrapper{
	display: inline-block;
    margin-left: 10px;
}

/* inside stripped table */
.litespeed-desc {
	font-size: 12px;
	font-weight: normal;
	color: #7a919e;
	margin: 10px 0;
	line-height: 1.7;
	/*max-width: 840px;*/
}

.litespeed-desc + .litespeed-desc {
	margin-top: -5px;
}

td > .litespeed-desc:first-child {
	margin-top: 0px;
	line-height: 2.24;
}

.litespeed-h3 {
	line-height: 18px;
	color: #264d73;
	font-size: 18px;
	font-weight: 600;
	margin: 2px 0;
}

.litespeed-div .submit {
	margin-top: 0;
}

@media screen and (min-width: 681px) {
	.litespeed-div {
		display: inline-block;
		min-width: 100px;
	}

	.litespeed-div .submit {
		margin: 5px;
		padding: 5px;
	}
}

@media screen and (max-width: 680px) {
	.litespeed-desc + .litespeed-desc.litespeed-left20 {
		margin-left: 0 !important;
	}

	.litespeed-desc .litespeed-callout.notice-warning.inline {
		word-break: break-word;
	}
}

.litespeed-h1 {
	display: inline-block;
}

h3 .litespeed-learn-more {
	font-size: 12px;
	font-weight: normal;
	color: #7a919e;
	margin-left: 30px;
}

.litespeed-wrap code {
	color: #666;
	background-color: #dde9f5;
	border-radius: 3px;
	font-size: 11px;
	font-style: normal;
}

.litespeed-wrap ul {
	margin-left: 2em;
}

.litespeed-wrap i {
	font-size: 13px;
	line-height: 16px;
}

.litespeed-wrap .litespeed-desc i {
	font-size: 12px;
}

.litespeed-wrap p {
	margin: 1em 0;
}

.litespeed-wrap p.submit {
	margin-bottom: 0;
}

.litespeed-desc p {
	margin-left: 0;
}

.litespeed-title,
.litespeed-title-short {
	font-size: 18px;
	border-bottom: 1px solid #cccccc;
	margin: 2.5em 0px 1.5em 0;
	display: table;
	padding-right: 50px;
	padding-left: 3px;
	padding-bottom: 3px;
}

.litespeed-title .button {
	margin-left: 1rem;
	margin-bottom: 5px;
	vertical-align: middle;
}

.litespeed-title .litespeed-quic-icon {
	margin-right: 6px;
}

.litespeed-title a,
.litespeed-title-short a {
	text-decoration: none;
}

.litespeed-title-short {
	padding-right: 20px;
}

.litespeed-title-section {
	margin: 2em -20px 12px -20px;
	padding: 12px 20px 12px 20px;
	border-bottom: 1px solid #eee;
	font-size: 1.2em;
	display: block;
	border-top: 1px solid #f1f1f1;
}

.litespeed-postbox .litespeed-title {
	display: flex;
	align-items: center;
}

.litespeed-title-right-icon {
	margin-left: auto;
	font-weight: normal;
}

.litespeed-list li:before {
	content: '>';
	color: #cc3d6a;
}

.litespeed-wrap a.disabled {
	cursor: not-allowed;
	opacity: 0.5;
	text-decoration: none;
	color: #72777c;
}

/* =======================================
			LAYOUT - table
======================================= */

.litespeed-table {
	font-size: 14px;
}

.litespeed-body tbody > tr > th {
	padding-left: 20px;
}

.litespeed-body tbody th {
	vertical-align: top;
	text-align: left;
	padding: 18px 10px 20px 0;
	width: 200px;
	font-weight: 600;
}

.litespeed-body td {
	padding: 15px 10px;
	line-height: 1.3;
	vertical-align: middle;
}

.litespeed-body .widefat td input + p {
	margin-top: 0.8em;
}

.litespeed-body .striped > tbody > :nth-child(even) .notice {
	background-color: #f9f9f9;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	border-right: 1px solid #e5e5e5;
}

.litespeed-body .striped > tbody > :nth-child(even) .notice:first-child {
	margin-top: 0;
}

/* small table inside */
.litespeed-body .litespeed-vary-table {
	margin-top: -5px;
	width: 250px;
	margin-bottom: 20px;
}

.litespeed-body .litespeed-vary-table td {
	width: 50%;
	padding: 5px 0px;
}

.litespeed-table-compact td,
.litespeed-table-compact th {
	padding: 0.5rem 0.75rem;
}

/* =======================================
			LAYOUT - block
======================================= */

.litespeed-block,
.litespeed-block-tiny {
	border: 1px dotted #cccccc;
	border-radius: 5px;
	display: flex;
	flex-wrap: wrap;
	padding: 0.75rem 1.25rem;
	margin-bottom: 5px;
}

.litespeed-block-tiny {
	max-width: 670px;
}

.litespeed-col {
	flex: 0 0 30%;
	padding-right: 2rem;
}

.litespeed-col:last-child,
.litespeed-col-auto:last-child {
	padding-right: 0;
}

.litespeed-col-auto {
	padding-right: 2rem;
}

.litespeed-col-br {
	flex: 0 0 100%;
	border-top: 1px dotted #cccccc;
}

.litespeed-col-inc {
	display: inline-block;
	margin-top: 16px;
	min-width: 150px;
	font-weight: bold;
}

.litespeed-block h4:first-child,
.litespeed-block .litespeed-form-label:not(.litespeed-form-label--toggle):first-child {
	margin-top: 0.5rem;
}

.litespeed-block .litespeed-callout:last-child {
	margin-bottom: 0;
}

@media screen and (max-width: 600px) {
	.litespeed-block {
		flex-direction: column;
	}

	.litespeed-block .litespeed-col {
		padding-right: 0;
	}
}

/* =======================================
			  CARDS LINKS
======================================= */

.litespeed-cards-wrapper,
.litespeed-panel-wrapper {
	display: flex;
	width: 100%;
	flex-flow: row wrap;
	justify-content: flex-start;
}

.litespeed-cards-wrapper {
	margin: -10px -15px -10px -15px;
}

.litespeed-panel {
	text-decoration: none;
	display: flex;
	justify-content: space-between;
	padding: 6px 8px 4px 5px;
	width: 322px;
	margin: 15px 5px 15px 15px;
	min-height: 75px;
	box-sizing: border-box;
	background: #f9fafc;
	transition: 0.25s;
}

.litespeed-panel:hover {
	border: 1px solid #6699cc;
	box-shadow: none;
}

.litespeed-panel-wrapper-icon {
	width: 25%;
	height: 100%;
}

[class*='litespeed-panel-icon-'] {
	background-size: contain;
	width: 60px;
	height: 60px;
	margin: 5px;
	background-repeat: no-repeat;
	display: inline-block;
}

.litespeed-panel-icon-all {
	background-image: url('../img/icons/all.svg');
}

.litespeed-panel-icon-revision {
	background-image: url('../img/icons/revision.svg');
}

.litespeed-panel-icon-orphaned_post_meta {
	background-image: url('../img/icons/revision.svg');
}

.litespeed-panel-icon-auto_draft {
	background-image: url('../img/icons/auto_draft.svg');
}

.litespeed-panel-icon-trash_post {
	background-image: url('../img/icons/trash_post.svg');
}

.litespeed-panel-icon-spam_comment {
	background-image: url('../img/icons/spam_comment.svg');
}

.litespeed-panel-icon-trash_comment {
	background-image: url('../img/icons/trash_comment.svg');
}

.litespeed-panel-icon-trackback-pingback {
	background-image: url('../img/icons/trackback-pingback.svg');
}

.litespeed-panel-icon-expired_transient {
	background-image: url('../img/icons/expired_transient.svg');
}

.litespeed-panel-icon-all_transients {
	background-image: url('../img/icons/all_transients.svg');
}

.litespeed-panel-icon-optimize_tables {
	background-image: url('../img/icons/optimize_tables.svg');
}

.litespeed-panel-icon-purge-front {
	background-image: url('../img/icons/purge-front.svg');
}

.litespeed-panel-icon-purge-pages {
	background-image: url('../img/icons/purge-pages.svg');
}

.litespeed-panel-icon-purge-cssjs {
	background-image: url('../img/icons/purge-cssjs.svg');
}

.litespeed-panel-icon-purge-object {
	background-image: url('../img/icons/purge-object.svg');
}

.litespeed-panel-icon-purge-opcache {
	background-image: url('../img/icons/purge-opcache.svg');
}

.litespeed-panel-icon-purge-all {
	background-image: url('../img/icons/purge-all.svg');
}

.litespeed-panel-icon-empty-cache {
	background-image: url('../img/icons/empty-cache.svg');
}

.litespeed-panel-icon-purge-403 {
	background-image: url('../img/icons/purge-403.svg');
}

.litespeed-panel-icon-purge-404 {
	background-image: url('../img/icons/purge-404.svg');
}

.litespeed-panel-icon-purge-500 {
	background-image: url('../img/icons/purge-500.svg');
}

.litespeed-panel-top-right-icon-cross {
	background-image: url('../img/icons/cross_icon.svg');
}

.litespeed-panel-top-right-icon-tick {
	background-image: url('../img/icons/success_icon.svg');
}

.litespeed-panel-content {
	width: 75%;
	height: 100%;
	margin-top: 7px;
}

.litespeed-panel-para {
	color: #264d73;
	font-size: 12px;
	line-height: 1.45;
}

.litespeed-panel .litespeed-h3 {
	font-size: 14px;
}

.litespeed-panel-counter {
	color: #3abfbf;
}

.litespeed-panel-counter-red {
	color: #cc3d6a;
}

.litespeed-panel-wrapper-top-right {
	width: 10%;
	height: 100%;
	text-align: right;
}

.litespeed-panel-top-right-icon-tick,
.litespeed-panel-top-right-icon-cross {
	background-size: contain;
	width: 20px;
	height: 20px;
	background-repeat: no-repeat;
	display: inline-block;
}

/* =======================================
	 BUTTONS
======================================= */

/* .litespeed-wrap .button{
	background:#fff;
} */

.litespeed-wrap .button-link {
	height: auto;
	line-height: inherit;
	font-size: inherit;
	box-shadow: none;
}

.litespeed-wrap .button-link:hover,
.litespeed-wrap .button-link:focus {
	box-shadow: none;
	background: none;
}

.litespeed .litespeed-btn-danger-bg,
.litespeed-wrap .litespeed-btn-danger-bg,
.litespeed-btn-danger-bg {
	background: #dc3545;
	color: #fff;
	border: 1px solid #cc3d6a;
	box-shadow: 0 1px 0 rgba(177, 93, 93, 0.5);
}

.litespeed .litespeed-btn-danger,
.litespeed-wrap .litespeed-btn-danger,
.litespeed-btn-danger {
	background: #fff;
	color: #cc3d6a;
	border: 1px solid #cc3d6a;
	box-shadow: 0 1px 0 rgba(177, 93, 93, 0.5);
}

.litespeed .litespeed-btn-danger:hover,
.litespeed-wrap .litespeed-btn-danger:hover,
.litespeed-btn-danger:hover {
	border-color: #ab244e;
	background: #cc3d6a;
	color: #fff;
}

.litespeed .litespeed-btn-warning,
.litespeed-wrap .litespeed-btn-warning,
.litespeed-btn-warning {
	background: #fff;
	color: #e59544;
	border: 1px solid #e59544;
	box-shadow: 0 1px 0 rgba(249, 166, 82, 0.55);
}

.litespeed .litespeed-btn-warning:hover,
.litespeed-wrap .litespeed-btn-warning:hover,
.litespeed-btn-warning:hover {
	border-color: #e59544;
	background: #e59544;
	color: #fff;
}

.litespeed .litespeed-btn-success,
.litespeed-wrap .litespeed-btn-success,
.litespeed-btn-success {
	background: #fff;
	color: #36b0b0;
	border: 1px solid #36b0b0;
	box-shadow: 0 1px 0 rgba(73, 160, 160, 0.55);
}

.litespeed .litespeed-btn-success:hover,
.litespeed-wrap .litespeed-btn-success:hover,
.litespeed-btn-success:hover {
	border-color: #36b0b0;
	background: #36b0b0;
	color: #fff;
}

.litespeed-wrap .button-primary {
	background: #528ac6;
	border-color: #538ac6 #2264ad #2264ad;
	color: #fff;
	box-shadow: 0 1px 0 #2264ad;
	text-shadow:
		0 -1px 1px #2264ad,
		1px 0 1px #2264ad,
		0 1px 1px #2264ad,
		-1px 0 1px #2264ad;
}

.litespeed-wrap .button-primary:focus,
.litespeed-wrap .button-primary:hover {
	background: #5891ce;
	border-color: #2264ad;
	color: #fff;
}

.litespeed-wrap .button-primary:hover {
	box-shadow: 0 1px 0 #2264ad;
}

.litespeed-wrap .button-primary:focus {
	background: #5891ce;
	border-color: #2264ad;
	color: #fff;
	box-shadow:
		0 1px 0 #0073aa,
		0 0 2px 1px #33b3db;
}

.litespeed .litespeed-btn-primary,
.litespeed-wrap .litespeed-btn-primary,
.litespeed-btn-primary {
	color: #538ac6;
	border: 1px solid #538ac6;
	-moz-box-shadow: 0 0 0 1px rgba(83, 138, 198, 0.25);
	-webkit-box-shadow: 0 0 0 1px rgba(83, 138, 198, 0.25);
	box-shadow: 0 0 0 1px rgba(83, 138, 198, 0.25);
}

.litespeed .litespeed-btn-primary:hover,
.litespeed-wrap .litespeed-btn-primary:hover,
.litespeed-btn-primary:hover {
	background: #538ac6;
	border-color: #538ac6;
	color: #fff;
}

.litespeed-wrap .button:not(.litespeed-btn-large) .dashicons {
	position: relative;
	top: -0.075em;
	vertical-align: middle;
}

.litespeed-wrap .button:not(:first-child) {
	margin-left: 5px;
}

.litespeed-wrap .button + .button {
	margin-left: 10px;
}

.litespeed-info-button {
	color: #c8c8c8;
	padding: 0;
	-webkit-appearance: none;
	border: none;
	background: none;
	vertical-align: middle;
	line-height: inherit;
	text-decoration: none;
}

.litespeed-info-button .dashicons {
	font-size: 16px;
	vertical-align: middle;
}

.litespeed-btn-pie {
	-webkit-appearance: none;
	background: none;
	border: none;
	border-radius: 0;
	box-shadow: none;
	padding: 0;
	margin: 0;
	top: -0.125em;
}

/* =======================================
   BUTTONS - sizes
======================================= */

.litespeed-wrap .litespeed-btn-tiny {
	padding: 2px 8px;
	line-height: 1.5;
	height: auto;
}

.litespeed-wrap .litespeed-btn-mini {
	padding: 0 8px 1px;
	font-size: 12px;
	font-weight: 600;
	margin: 5px 0;
}

.litespeed-wrap .litespeed-btn-mini .dashicons.dashicons-image-rotate {
	padding-top: 3px;
	font-size: 18px;
}

.litespeed-wrap .litespeed-btn-mini .dashicons {
	padding-top: 2px;
}

.litespeed-wrap .litespeed-btn-large {
	font-size: 1.5em;
	padding: 0.75em 1.5em;
	margin: 0 0.25em;
	height: auto;
}

.litespeed-wrap .litespeed-btn-large .dashicons {
	font-size: 1.25em;
	width: auto;
}

/* =======================================
	  SWITCH
======================================= */

.litespeed-switch {
	font-size: 14px;
	font-weight: 600;
	margin: 0 0 0;
	display: inline-flex;
	position: relative;
}

.rtl .litespeed-switch {
	flex-direction: row-reverse;
}

.litespeed-switch input:checked:active + label {
	box-shadow:
		0 2px 0 rgba(27, 146, 146, 0.7),
		inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
}

.litespeed-switch input:checked + label {
	background-color: #36b0b0;
	color: #fff;
	border: 1px solid #36b0b0;
	box-shadow: 0 2px 0 #1b9292;
	z-index: 2;
	text-shadow:
		0 -1px 1px #1b9292,
		1px 0 1px #1b9292,
		0 1px 1px #1b9292,
		-1px 0 1px #1b9292;
}

.litespeed-switch label {
	font-size: 14px;
	display: inline-block;
	min-width: 72px;
	background-color: #f9fafc;
	font-weight: 400;
	text-align: center;
	padding: 6px 12px 5px 12px;
	cursor: pointer;
	border: 1px solid #ccc;
	border-bottom: none;
	box-shadow: 0 2px 0 #ccc;
	position: relative;
}

.litespeed-switch label:not(:last-child) {
	margin-right: -1px;
}

.litespeed-switch label:last-child {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

.litespeed-switch label:first-of-type {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
}

.litespeed-switch input:hover + label {
	border-color: #1a9292;
	box-shadow: 0 2px 0 #1a9292;
	z-index: 2;
	color: #117171;
}

.litespeed-switch input:focus + label {
	color: #117171;
	box-shadow: 0 0px 0px 2px rgba(28, 138, 128, 0.85);
	border-color: transparent;
	z-index: 2;
}

.litespeed-switch input:focus + label + input + input:hover + label,
.litespeed-switch input:focus + label + input:hover + label {
	z-index: 1;
}

.litespeed-switch input:active + label {
	box-shadow:
		0 2px 0 #1b9292,
		inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
}

.litespeed-switch input:checked:hover + label,
.litespeed-switch input:checked:focus + label {
	background-color: #36b0b0;
	color: #fff;
}

.litespeed-switch input {
	display: inline-block;
	position: absolute;
	z-index: -1;
	margin: 0;
}

.litespeed-cache-purgeby-text {
	margin: 0;
	display: inline-block;
}

/* =======================================
				TOGGLE
======================================= */

.litespeed-toggle-stack {
	display: flex;
	flex-direction: column;
}

.litespeed-toggle-stack .litespeed-toggle-wrapper {
	justify-content: space-between;
}

.litespeed-toggle-wrapper {
	display: flex;
	align-items: center;
}

.litespeed-toggle-wrapper + .litespeed-toggle-wrapper {
	margin-top: 0.75rem;
}

.litespeed-toggle {
	position: relative;
	overflow: hidden;
	min-width: 58px;
	height: 21px;
	/*margin-left: 1.2rem;*/
}

.litespeed-toggle-group {
	position: absolute;
	width: 200%;
	top: 0;
	bottom: 0;
	left: 0;
	transition: left 0.35s;
	-webkit-transition: left 0.35s;
	-moz-user-select: none;
	-webkit-user-select: none;
}

.litespeed-toggle-on {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 50%;
	margin: 0;
	border: 0;
	border-radius: 0;
}

.litespeed-toggle-on.litespeed-toggle-btn {
	padding-right: 24px;
}

.litespeed-toggle-off.litespeed-toggle-btn {
	padding-left: 24px;
}

.litespeed-toggle-handle {
	position: relative;
	margin: 0 auto;
	padding-top: 0px;
	padding-bottom: 0px;
	height: 100%;
	width: 0px;
	border-width: 0 1px;
}

.litespeed-toggle-off {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 50%;
	right: 0;
	margin: 0;
	border: 0;
	border-radius: 0;
}

.litespeed-toggleoff .litespeed-toggle-group {
	left: -100%;
}

.litespeed-toggle-btn {
	display: inline-block;
	padding: 5px 10px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px;
}

.litespeed-toggle-btn-primary {
	color: #fff;
	background-color: #36b0b0;
	border-color: #36b0b0;
}

.litespeed-toggle-btn-default {
	color: #333;
	background-color: #fff;
	border-color: #ccc;
}

.litespeed-toggle-btn-success:hover,
.litespeed-toggle-btn-success:focus,
.litespeed-toggle-btn-success:active,
.litespeed-toggle-btn-success.litespeed-toggle-active {
	color: #fff;
	background-color: #00bfbf;
	border-color: #6699cc;
}

.litespeed-toggle-btn-default:hover,
.litespeed-toggle-btn-default:focus,
.litespeed-toggle-btn-default:active,
.litespeed-toggle-btn-default.litespeed-toggle-active {
	color: #333;
	background-color: #e6e6e6;
	border-color: #adadad;
}

.litespeed-toggle-btn:active,
.litespeed-toggle-btn.litespeed-toggle-active {
	background-image: none;
	outline: 0;
	-webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.litespeed-toggle-btn-default:active,
.litespeed-toggle-btn-default.litespeed-toggle-active {
	background-image: none;
}

/* =======================================
	LABEL/TAG
======================================= */
[class*='litespeed-label-'] {
	display: inline;
	padding: 0.2em 0.6em 0.3em;
	font-size: 75%;
	font-weight: bold;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: 0.25em;
}

[class*='litespeed-label-']:hover,
[class*='litespeed-label-']:focus {
	color: #fff;
	text-decoration: none;
	cursor: pointer;
}

[class*='litespeed-label-']:empty {
	display: none;
}

.litespeed-label-regular {
	font-size: 1em;
}

.litespeed-label-default {
	background-color: #777;
}

.litespeed-label-default[href]:hover,
.litespeed-label-default[href]:focus {
	background-color: #5e5e5e;
}

.litespeed-label-primary {
	background-color: #337ab7;
}

.litespeed-label-primary[href]:hover,
.litespeed-label-primary[href]:focus {
	background-color: #286090;
}

.litespeed-label-success {
	background-color: #5cb85c;
}

.litespeed-label-success[href]:hover,
.litespeed-label-success[href]:focus {
	background-color: #449d44;
}

.litespeed-label-info {
	background-color: #5bc0de;
}

.litespeed-label-info[href]:hover,
.litespeed-label-info[href]:focus {
	background-color: #31b0d5;
}

.litespeed-label-warning {
	background-color: #f0ad4e;
}

.litespeed-label-warning[href]:hover,
.litespeed-label-warning[href]:focus {
	background-color: #ec971f;
}

.litespeed-label-danger {
	background-color: #d9534f;
}

.litespeed-label-danger[href]:hover,
.litespeed-label-danger[href]:focus {
	background-color: #c9302c;
}

/* =======================================
	   SHELL
======================================= */
.litespeed-shell {
	width: 98%;
	background: #141414;
	margin: 20px auto 0 10px;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	position: relative;
	height: 224px;
}

.litespeed-shell-header {
	z-index: 999;
	position: absolute;
	top: 0;
	right: 0;
	width: 50px;
	height: 34px;
	padding: 5px 0;
}

.litespeed-shell-header-bg {
	opacity: 0.4;
	background-color: #cccccc;
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
	z-index: 4;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-top-radius: 3px;
}

.litespeed-shell-header-bar {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 10;
	height: 2px;
	background-color: #f48024;
}

.litespeed-shell-header-icon-container {
	position: absolute;
	top: 10px;
	right: 10px;
	width: 29px;
	height: 34px;
	z-index: 6;
}

ul.litespeed-shell-body {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	overflow-y: scroll;
	margin: 0;
	padding: 5px;
	list-style: none;
	background: #141414;
	color: #45d40c;
	font:
		0.8em 'Andale Mono',
		Consolas,
		'Courier New';
	line-height: 1.6em;

	-webkit-border-bottom-right-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;
}

.litespeed-shell-body li:before {
	content: '>';
	position: absolute;
	left: 0;
	top: 0;
}

.litespeed-shell-body li {
	word-wrap: break-word;
	position: relative;
	padding: 0 0 0 15px;
	margin: 0;
}

.litespeed-widget-setting {
	background-color: #ecebdc;
	padding: 5px 14px;
	margin: 5px -15px;
}

/* =======================================
			CALLOUT / NOTICE
======================================= */

.litespeed-callout {
	margin: 1.5rem 0;

	border-right: 1px solid #e5e5e5;
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	background: #f9f9f9;
}

.litespeed-callout h4:not(:last-child) {
	margin-bottom: 0.5rem;
	margin-top: 1em;
}

.litespeed-callout p {
	margin-left: 0;
}

.litespeed-callout ol,
.litespeed-callout ul {
	margin-left: 1em;
}

.litespeed-callout.notice-warning h4 {
	color: #e59544;
}

.litespeed-callout.notice-error h4 {
	color: #dc3232;
}

.litespeed-callout-bg {
	margin: 1.5rem 0;
	background: #f9f9f9;
	border-top: none;
	border-bottom: none;
	border-right: none;
}

/* =======================================
			TICK / CHECKBOX
======================================= */

.litespeed-tick-wrapper {
	margin-left: -5px;
}

.litespeed-tick {
	display: inline-block;
	/* min-width: 125px; */
	background: #f2f9ff;
	padding: 5px 0 5px 0px;
	border-radius: 3px;
	cursor: pointer;
	margin: 5px 5px 5px 0;
}

.litespeed-tick-list .litespeed-tick {
	display: block;
	margin-bottom: 3px;
	margin-top: 0;
	background: none;
}

.litespeed-tick-list .litespeed-tick input[type='checkbox'] {
	margin-left: 0;
}

.litespeed-tick-list .litespeed-tick label {
	color: inherit;
}

.litespeed-tick input[type='checkbox'] {
	height: 18px;
	width: 18px;
	vertical-align: middle;
	margin: 0 10px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	-webkit-border-radius: 3px;
	border-radius: 3px;

	cursor: pointer;
}

.litespeed-tick input[type='checkbox']:not(:disabled):hover {
	border-color: #538ac6;
}

.litespeed-tick input[type='checkbox']:active:not(:disabled) {
	border-color: #538ac6;
}

.litespeed-tick input[type='checkbox']:focus {
	outline: none;
}

.litespeed-tick input[type='checkbox']:checked {
	border-color: #538ac6;
	background-color: #538ac6;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.litespeed-tick input[type='checkbox']:checked:before {
	content: '';
	display: block;
	width: 5px;
	height: 11px;
	border: solid #fff;
	border-width: 0 2px 2px 0;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	margin-left: 5px;
	margin-top: -1px;
	cursor: pointer;
}

.litespeed-tick label {
	padding: 2px 0px 2px 0;
	font-size: 14px;
	color: #264d73;
}

.litespeed-tick label:hover {
	min-width: 115px;
	color: #6699cc;
}

/* =======================================
			RADIO - vertical
======================================= */

.litespeed-radio-row {
	margin-bottom: 12px;
	position: relative;
	padding-left: 1.5rem;
}

.litespeed-radio-row input[type='radio'] {
	margin-top: 0;
	margin-bottom: 0;
	position: absolute;
	line-height: 1;
	left: 0;
	top: 0.7em;
	transform: translateY(-50%);
}

.litespeed-radio-row label {
	vertical-align: text-bottom;
	line-height: 1.4;
}

@media screen and (max-width: 782px) {
	.litespeed-radio-row {
		padding-left: 2rem;
	}
}

/* =======================================
		   FORM - layout
======================================= */

.litespeed-wrap .litespeed-float-submit {
	position: absolute;
	right: 0;
	top: -5px;
	margin-top: 0;
}

.rtl .litespeed-wrap .litespeed-float-submit {
	left: 10px;
	right: unset;
}

.litespeed-wrap .litespeed-float-resetbtn {
	position: absolute;
	right: 0;
	bottom: 20px;
}

.rtl .litespeed-wrap .litespeed-float-resetbtn {
	left: 10px;
	right: unset;
}

/* =======================================
		  FORM - utilities
======================================= */

.litespeed .litespeed-input-large {
	font-size: 20px;
}

.litespeed-input-long {
	width: 87%;
}

.litespeed-input-short2 {
	width: 150px;
}

.litespeed-input-short {
	width: 45px;
}

@media screen and (max-width: 680px) {
	.litespeed-input-short2 {
		width: 160px;
	}

	.litespeed-input-short {
		width: 50px;
	}
}

/* =======================================
		   FORM - elements
======================================= */

.litespeed-form-label {
	font-size: 1em;
	margin: 0.65rem 0;
	display: block;
	font-weight: 600;
}

.litespeed-form-label--toggle {
	margin: 0;
	display: inline-block;
	min-width: 110px;
}

input.litespeed-input[type='file'] {
	padding: 9px;
	min-width: 500px;
	border: 1px solid #ddd;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
	background-color: #fff;
	color: #32373c;
	outline: 0;
	transition: 50ms border-color ease-in-out;
}

.litespeed-body .litespeed-textarea-success {
	border-color: #6699cc;
}

input.litespeed-input-success {
	border-color: #28a745;
}

input.litespeed-input-warning {
	border-color: #e59544;
}

.litespeed-textarea {
	width: 60%;
}

.litespeed-textarea-recommended {
	display: flex;
	margin-top: -5px;
}

.litespeed-textarea-recommended .litespeed-desc {
	margin: 0;
}

.litespeed-textarea-recommended > div:first-child {
	margin-top: 1.7em;
	font-size: 12px;
	margin-right: 25px;
}

.litespeed-wrap .litespeed-collection-button {
	text-decoration: none;
	min-width: 30px;
	text-align: center;
}

.litespeed-collection-button[data-action='add'] {
	margin-top: -5px;
	margin-left: -5px;
}

.litespeed-collection-button .dashicons {
	vertical-align: baseline;
}

.litespeed-wrap .button:not(.litespeed-btn-large).litespeed-form-action .dashicons {
	font-size: 1.2em;
	vertical-align: middle;
	top: 0;
}

@media screen and (max-width: 680px) {
	.litespeed-body tbody > tr > th {
		display: block;
		padding: 18px 0 5px 12px;
	}

	.litespeed-body .litespeed-table td {
		display: block;
		max-width: 100%;
	}

	.litespeed-body .litespeed-table textarea,
	.litespeed-body .litespeed-table input.litespeed-regular-text {
		width: 100% !important;
	}

	.litespeed-wrap .litespeed-float-submit {
		display: none;
	}

	.litespeed-body {
		padding: 1px 10px 20px 15px;
	}

	.litespeed-body .regular-text:not(.litespeed-input-short) {
		width: 100%;
	}

	.litespeed-textarea-recommended {
		flex-direction: column;
	}

	.litespeed-textarea-recommended > div:first-child {
		margin-bottom: 1.7em;
		margin-top: 0;
		margin-right: 0;
	}

	.litespeed-switch {
		max-width: 100%;
		flex-wrap: wrap;
	}

	.litespeed-switch + .litespeed-warning {
		display: block;
		margin-top: 10px;
	}

	input.litespeed-input[type='file'] {
		max-width: calc(100% - 24px);
		min-width: 0;
	}

	.litespeed-body .litespeed-table .litespeed-row-flex {
		flex-direction: column;
	}
}

/* =======================================
		   ENTERPRISE NOTICE
======================================= */

.litespeed-ent-notice {
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background-color: #333;
	z-index: 999;
	opacity: 0.8;
	text-align: center;
	font-size: 3rem;
	color: #1865c5;
}

.litespeed-ent-notice-desc {
	position: relative;
	top: 30%;
	transform: rotate(-20deg);
	text-shadow: 2px 2px 4px #000000;
}

/* =======================================
			  PROMO BANNER
======================================= */

.litespeed-banner-promo,
.litespeed-banner-promo-full {
	display: flex;
	padding: 0px;
}

.litespeed-banner-promo-full {
	margin: 0px;
	padding: 0px;
}

.litespeed-banner-promo-logo {
	background-image: url(../img/lscwp-logo_90x90.png);
	background-size: contain;
	width: 90px;
	background-repeat: no-repeat;
	display: inline-block;
}

.litespeed-banner-promo-full .litespeed-banner-promo-logo {
	margin: 0px;
	width: 90px;
	height: 90px;
}

.litespeed-banner-promo-content {
	margin-left: 25px;
}

.litespeed-banner-promo-full .litespeed-banner-promo-content {
	width: 75%;
}

.litespeed-banner-promo-content h1 {
	font-weight: 600;
	color: #538ac6;
	margin-top: 10px;
}

.litespeed-banner-title {
	font-size: 1.3em;
	margin: 8px 0px 5px 0px;
}

.litespeed-banner-promo-slacklogo {
	background-image: url('../img/slack-logo.png');
	background-size: contain;
	width: 75px;
	height: 75px;
	background-repeat: no-repeat;
	display: inline-block;
	padding: 0px;
	flex: 0 0 5%;
}

.litespeed-banner-promo .litespeed-banner-promo-slack-line1 {
	font-size: 18px;
	margin-top: 0px;
	line-height: 21px;
}

.litespeed-banner-promo .litespeed-banner-promo-slack-textlink {
	color: #e59544;
	text-decoration: none;
}

.litespeed-banner-promo .litespeed-banner-promo-slack-textlink:hover {
	opacity: 0.8;
}

.litespeed-banner-promo-slack-line2 {
	font-size: 15px;
	margin: 0px;
	line-height: 0.75em;
}

.litespeed-banner-promo-slack-link {
	color: #888888;
}

a.litespeed-btn-xs.litespeed-banner-promo-slack-btn {
	margin: 0px 5px;
}

/* =======================================
			  PROMO BANNER - QC
======================================= */

.litespeed-banner-promo-qc {
	display: flex;
}

.litespeed-banner-promo-qc h2 {
	line-height: 1.4;
}

.litespeed-banner-promo-qc-content {
	display: flex;
	align-items: center;
}

.litespeed-banner-promo-qc-description {
	flex-basis: 50%;
	padding-right: 2rem;
}

.litespeed-banner-promo-qc-description p {
	font-size: 14px;
}

.litespeed-banner-promo-qc-description .button {
	margin-right: 1.5rem;
}

.litespeed-tweet-preview {
	border-radius: 5px;
	line-height: 1.3125;
	box-shadow: 1px 1px 0.5em rgba(0, 0, 0, 0.3);
	margin: 0.5em 1em 1em 0;
	padding: 1em;
	max-width: 480px;
	display: flex;
}

.litespeed-tweet-preview:after {
	content: '';
	display: block;
	clear: both;
}

.litespeed-tweet-preview p:first-child {
	margin-top: 0;
}

.litespeed-tweet-preview-title {
	color: #777;
	margin-top: 0.9em;
	font-weight: normal;
	font-size: 12px;
	margin-bottom: 0;
	margin-top: 0.9em;
}

.litespeed-tweet-text {
	font:
		14px system-ui,
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		Ubuntu,
		'Helvetica Neue',
		sans-serif;
	line-height: 1.3125;
}

.litespeed-tweet-cta {
	text-align: right;
	margin-top: 1em;
}

.litespeed-tweet-cta a {
	background-color: #1da1f2;
	line-height: 1.3125;
	color: #fff;
	font-weight: bold;
	display: inline-flex;
	padding: 0.55em 1em;
	font-size: 14px;
	border-radius: 99em;
	text-decoration: none;
}

.litespeed-tweet-cta a:hover {
	background-color: #1e98e1;
}

.litespeed-tweet-cta a svg {
	width: 16px;
	height: 18px;
	margin-right: 0.5em;
}

.litespeed-tweet-cta a svg path {
	fill: currentColor;
}

.litespeed-tweet-img {
	width: calc(240px + 1rem);
	padding-right: 1rem;
	box-sizing: border-box;
}

.litespeed-tweet-img img {
	max-width: 100%;
	vertical-align: middle;
}

.litespeed-tweet-img + p {
	margin-top: 0;
}

/* =======================================
		admin -> media lib icon
======================================= */

.litespeed-media-href {
	display: inline-table;
}

[class*='litespeed-icon-media-'] {
	background-size: contain;
	width: 25px;
	height: 25px;
	vertical-align: middle;
	margin: 0;
	background-repeat: no-repeat;
	display: inline-block;
}

[class*='litespeed-icon-media-']:hover {
	opacity: 0.7;
}

.litespeed-icon-media-webp {
	background-image: url('../img/icons/img_webp.svg');
}

.litespeed-icon-media-webp-disabled {
	background-image: url('../img/icons/img_webp_disabled.svg');
}

.litespeed-icon-media-optm {
	background-image: url('../img/icons/img_optm.svg');
}

.litespeed-icon-media-optm-disabled {
	background-image: url('../img/icons/img_optm_disabled.svg');
}

p.litespeed-media-p {
	margin-bottom: 1px !important;
}

p.litespeed-txt-webp {
	color: #83b04a;
}

p.litespeed-txt-ori {
	color: #5967b3;
}

p.litespeed-txt-disabled {
	color: #ced2d9;
}

.litespeed-media-svg {
	vertical-align: middle;
	margin: 5px;
	width: 25px;
	height: auto;
}

@keyframes litespeed-circle-chart-fill {
	to {
		stroke-dasharray: 0 100;
	}
}

/* =======================================
			 PIE chart
======================================= */

.litespeed-pie {
	vertical-align: middle;
	margin: 5px 5px 5px 0;
}

circle.litespeed-pie_bg {
	stroke: #efefef;
	stroke-width: 2;
	fill: none;
}

circle.litespeed-pie_circle {
	animation: litespeed-circle-chart-fill 2s reverse;
	transform: rotate(-90deg);
	transform-origin: center;

	animation: litespeed-pie-fill 2s reverse;
	/* 1 */
	stroke: #28a745;
	stroke-width: 2;
	stroke-linecap: round;
	fill: none;
}

.litespeed-pie.litespeed-pie-tiny {
	margin: 0 2px 0 0;
}

.litespeed-pie.litespeed-pie-tiny text {
	font-weight: bold;
	fill: #828282;
}

.litespeed-pie.litespeed-pie-tiny circle {
	stroke-linecap: initial;
}

.litespeed-pie-tiny circle.litespeed-pie_bg,
.litespeed-pie-tiny circle.litespeed-pie_circle {
	stroke-width: 3;
}

.litespeed-pie-tiny circle.litespeed-pie_bg {
	stroke: #eee;
}

.litespeed-pie-success circle.litespeed-pie_circle {
	stroke: #28a745;
}

.litespeed-pie-warning circle.litespeed-pie_circle {
	stroke: #e67700;
}

.litespeed-pie-danger circle.litespeed-pie_circle {
	stroke: #c7221f;
}

g.litespeed-pie_info text {
	dominant-baseline: central;
	text-anchor: middle;
	font-size: 11px;
}

.litespeed-promo-score g.litespeed-pie_info text {
	font-size: 14px;
	font-weight: 600;
}

.litespeed-pie-success g.litespeed-pie_info text {
	fill: #28a745;
}

.litespeed-pie-warning g.litespeed-pie_info text {
	fill: #e67700;
}

.litespeed-pie-danger g.litespeed-pie_info text {
	fill: #c7221f;
}

g.litespeed-pie_info .litespeed-pie-done {
	fill: #28a745;
	font-size: 15px;
}

/* =======================================
		VIEW - multiple cdn mapping
======================================= */

[data-litespeed-cdn-mapping]:first-child [data-litespeed-cdn-mapping-del] {
	display: none;
}

.litespeed-cdn-mapping-col1 {
	padding-right: 2rem;
	max-width: 35%;
}

.litespeed-cdn-mapping-col1 .litespeed-input-long {
	width: 100%;
}

.litespeed-cdn-mapping-col2 {
	padding-top: 0.25rem;
}

.litespeed-cdn-mapping-col1 label {
	position: relative;
}

[data-litespeed-cdn-mapping-del] {
	position: absolute;
	right: -6px;
	top: -6px;
}

@media screen and (max-width: 600px) {
	.litespeed-cdn-mapping-col1 {
		max-width: 100%;
	}
}

/* =======================================
		VIEW - crawler
======================================= */

.litespeed-crawler-curr {
	vertical-align: middle;
	height: 20px;
	margin-left: 10px;
}

#cookie_crawler > p:first-child {
	margin-top: 5px;
}

.litespeed-crawler-sitemap-nav {
	display: flex;
	justify-content: space-between;
}

.litespeed-crawler-sitemap-nav > div {
	margin-top: 10px;
}

@media screen and (max-width: 680px) {
	.litespeed-crawler-sitemap-nav {
		display: block;
	}

	.litespeed-table-responsive {
		clear: both;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
	}

	.litespeed-table-responsive table {
		width: 100%;
	}

	.litespeed-table-responsive th {
		text-wrap: nowrap;
	}

	.litespeed-table-responsive [data-crawler-list].wp-list-table td:nth-child(2) {
		min-width: 115px;
	}

	.litespeed-wrap input[name='kw'] {
		width: 100% !important;
	}
}

/* =======================================
			PROGRESS BAR
======================================= */

.litespeed-progress-bar {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	color: #fff;
	text-align: center;
	background-color: #007bff;
	transition: width 0.6s ease;
}

.litespeed-progress-bar-yellow {
	background-color: #fbe100;
}

.litespeed-progress {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	height: 12px;
	overflow: hidden;
	font-size: 0.75rem;
	background-color: #e9ecef;
	border: 1px solid #dddddd;
	border-radius: 8px;
	width: 75%;
	margin: 5em 1em 1.5em 1em !important;
}

/* =======================================
		PROGRESS BAR - modal
======================================= */

.litespeed-modal {
	margin-top: -8px;
}

.litespeed-modal .litespeed-progress {
	margin-left: -8px;
	margin-right: -8px;
}

/* =======================================
		   		GUIDANCE
======================================= */

.litespeed-guide {
	border: 1px solid #73b38d;
	max-width: 50%;
	padding: 20px;
}

.litespeed-guide h2 {
	color: #73b38d;
	border-bottom: 1px solid #73b38d;
	display: table;
	padding-right: 50px;
	padding-left: 3px;
	padding-bottom: 3px;
}

.litespeed-guide li {
	font-size: 15px;
	line-height: 30px;
	margin: 10px 10px 10px 16px;
}

.litespeed-guide li.litespeed-guide-done:before {
	content: '\2713';
	font-size: 26px;
	color: #73b38d;
	margin-left: -37px;
	margin-right: 18px;
	opacity: 1;
}

.litespeed-guide li.litespeed-guide-done {
	opacity: 0.9;
}

/* =======================================
		VIEW - image optimization
======================================= */

.litespeed-image-optim-summary-wrapper {
	padding: 0;
}

.litespeed-cache_page_litespeed-img_optm .nav-tab-wrapper,
.litespeed-cache_page_litespeed-cdn .nav-tab-wrapper {
	border-bottom-color: #e5e5e5;
}

.litespeed-cache_page_litespeed-img_optm .litespeed-body,
.litespeed-cache_page_litespeed-cdn .litespeed-body {
	box-shadow: none;
}

.litespeed-cache_page_litespeed-img_optm .litespeed-wrap .nav-tab:not(.nav-tab-active),
.litespeed-cache_page_litespeed-cdn .litespeed-wrap .nav-tab:not(.nav-tab-active) {
	border-bottom-color: #e5e5e5;
}

.litespeed-cache_page_litespeed-img_optm .nav-tab-active,
.litespeed-cache_page_litespeed-cdn .nav-tab-active {
	border-left-color: #e5e5e5;
	border-right-color: #e5e5e5;
	border-top-color: #e5e5e5;
	position: relative;
	z-index: 2;
}

.litespeed-cache_page_litespeed-img_optm [data-litespeed-layout='summary'],
.litespeed-cache_page_litespeed-cdn [data-litespeed-layout='qc'] {
	margin: -2px -21px -21px -21px;
	background: #f0f0f1;
}

.litespeed-column-secondary {
	background: #f9fafc;
}

.litespeed-column-with-boxes .postbox {
	border-color: #e5e5e5;
}

.litespeed-column-with-boxes .litespeed-width-7-10 {
	padding: 0;
}

@media screen and (min-width: 815px) {
	.litespeed-column-with-boxes > div.litespeed-column-left {
		padding-right: 25px;
	}
}

.litespeed-column-with-boxes > div.litespeed-column-right {
	background: #f1f1f1;
	padding-top: 0;
	padding-right: 0;
	padding-left: 0;
}

.litespeed-column-with-boxes > div.litespeed-column-right .litespeed-postbox:last-child {
	margin-bottom: 0;
}

.litespeed-image-optim-summary,
.litespeed-column-left-inside {
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	position: relative;
	padding: 1px 20px 20px 20px;
	background: #fff;
	border: 1px solid #e5e5e5;
}

.litespeed-image-optim-summary-footer,
.litespeed-column-with-boxes-footer {
	border-top: 1px solid #efefef;
	background: #f9f9f9;
	padding: 20px;
	margin: 20px -20px -20px;
}

.litespeed-help-btn-icon {
	text-decoration: none;
	margin-left: 10px;
	color: #c8c8c8;
}

.litespeed-postbox-imgopt-info .litespeed-flex-container {
	align-items: center;
}

.litespeed-postbox-imgopt-info .litespeed-flex-container:not(:last-child) {
	margin-bottom: 0.65em;
}

.litespeed-postbox-imgopt-info .litespeed-flex-container p:first-child {
	margin-top: 0;
}

.litespeed-image-optim-summary > h3:first-child,
.litespeed-column-left-inside > h3:first-child {
	margin-top: 1.6em;
	font-size: 1.2em;
}

.litespeed-image-optim-summary > h3:first-child .litespeed-quic-icon,
.litespeed-column-left-inside > h3:first-child .litespeed-quic-icon {
	width: 1.2em;
	height: 1.4em;
	background-size: contain;
	margin-right: 0.2rem;
}

.litespeed-img-optim-actions {
	margin-top: 1.65em;
	display: flex;
	align-items: flex-end;
	flex-wrap: wrap;
}

.litespeed-img-optim-actions .button-primary {
	font-size: 1.2em;
	margin-right: 1em;
	padding: 0.35em 0.85em;
	min-width: 210px;
	text-align: center;
}

@media screen and (max-width: 1079px) {
	.litespeed-postbox-imgopt-info svg {
		height: 50px;
		width: 50px;
	}
}

@media screen and (max-width: 814px) {
	.litespeed-column-with-boxes > div:first-child {
		padding-right: 0;
		margin-bottom: 1rem;
	}
}

@media screen and (max-width: 680px) {
	.litespeed-img-optim-actions .button + .button.button-secondary {
		margin-left: 0;
		margin-top: 10px;
	}
}

/* =======================================
	VIEW - image optm media row
======================================= */

.imgoptm.column-imgoptm a[data-balloon-pos] {
	border-bottom: 1px dashed;
}

.imgoptm.column-imgoptm p {
	margin-bottom: 0.25em;
	margin-top: 0;
}

.imgoptm.column-imgoptm p + .row-actions {
	margin-top: 0.5em;
}

.fixed .column-lqip {
	width: 6rem;
}

.litespeed-media-lqip img {
	max-width: 62px;
	max-height: 62px;
}

.litespeed-media-href {
	font-size: 12px;
}

/* =======================================
		VIEW - log view
======================================= */

.litespeed-log-view-wrapper {
	margin: 1.5em 0;
}

/* =======================================
			VIEW - dashboard
======================================= */

.litespeed-dashboard-group {
	margin-bottom: 1rem;
}

.litespeed-dashboard-group > .litespeed-flex-container {
	margin: 0 -10px;
	min-width: 100%;
	width: auto;
}

.litespeed-dashboard .litespeed-postbox {
	margin: 10px;
}

.litespeed-dashboard-title a {
	text-decoration: none;
	margin-left: 0.25rem;
}

.litespeed-dashboard-title--w-btn {
	display: flex;
	align-items: center;
}

.litespeed-dashboard-title--w-btn .button {
	font-weight: normal;
}

.litespeed-postbox-footer .button-small {
	vertical-align: middle;
}

.litespeed-postbox .button.button-small .dashicons,
.litespeed-dashboard-title--w-btn .button.button-small .dashicons {
	font-size: 1rem;
	top: 0.05em;
	vertical-align: middle;
	margin-left: -5px;
}

.litespeed-dashboard-header {
	display: flex;
	align-items: center;
}

.litespeed-postbox p.litespeed-dashboard-stats-total + p.litespeed-dashboard-stats-total {
	margin-top: 1.2em;
}

.litespeed-dashboard-header:first-child {
	margin-top: 1.5rem;
}

.litespeed-dashboard-header hr {
	align-self: center;
	flex-grow: 1;
	margin-left: 15px;
	margin-right: 15px;
}

.litespeed-dashboard-header hr:last-child {
	margin-right: 0;
}

.litespeed-dashboard-header .litespeed-learn-more {
	font-weight: normal;
	text-decoration: none;
	margin-top: -2px;
	color: #5e7380;
}

.litespeed-dashboard-stats h3 {
	text-transform: uppercase;
	font-size: 12px;
	font-weight: normal;
	margin-bottom: 0;
	margin-top: 1.2em;
	color: #777;
}

.litespeed-dashboard-stats h3 + p {
	margin-top: 0;
	margin-bottom: 0;
}

.litespeed-dashboard-stats .litespeed-desc {
	color: #777;
}

.litespeed-dashboard-stats p strong {
	font-size: 2em;
	font-weight: normal;
	margin-right: 5px;
}

.litespeed-dashboard-stats-wrapper {
	display: flex;
	position: relative;
}

.litespeed-dashboard-stats-wrapper .litespeed-postbox {
	margin: 0;
	min-width: 20%;
}

.litespeed-dashboard-stats-wrapper .litespeed-postbox .inside .litespeed-title,
.litespeed-dashboard-group .litespeed-postbox .inside .litespeed-title {
	font-size: 14px;
}

.litespeed-postbox .inside .litespeed-title a {
	font-size: 13px;
}

.litespeed-dashboard-stats-wrapper .litespeed-postbox:not(:last-child) {
	margin-right: -1px;
}

.litespeed-dashboard-stats-wrapper .litespeed-postbox:not(:first-child) {
	border-left-color: #f9f9f9;
}

.litespeed-dashboard-stats-wrapper .litespeed-dashboard-stats p strong {
	font-size: 1.4rem;
}

.litespeed-dashboard-stats-wrapper .litespeed-pie {
	width: 60px;
	height: 60px;
}

.litespeed-dashboard-stats-wrapper .litespeed-flex-container + p:not(:last-child) {
	margin-bottom: 0.55em;
}

.litespeed-dashboard-stats-payg {
	color: #777;
}

.litespeed-dashboard-stats-payg strong {
	color: #444;
}

.postbox .inside > p.litespeed-dashboard-stats-payg {
	margin-top: 1.35em;
}

.postbox .inside > p.litespeed-dashboard-stats-payg:last-child {
	margin-bottom: -5px !important;
}

.litespeed-postbox p.litespeed-dashboard-stats-total {
	padding: 0.75em 20px 0 20px;
	border-top: 1px dashed #eee;
	margin-top: 0.55em;
	margin-left: -20px;
	margin-right: -20px;
	margin-bottom: -0.55em !important;
}

.litespeed-postbox.litespeed-postbox-partner .inside {
	margin: 11px 0;
}

.litespeed-dashboard-stats-wrapper .litespeed-postbox.litespeed-postbox-partner h3.litespeed-title {
	color: #777;
	font-weight: normal;
	font-size: 13px;
}

.litespeed-postbox.litespeed-postbox-partner a {
	font-size: 1.35rem;
	font-weight: bold;
	text-decoration: none;
	margin-top: 5px;
	max-width: 100%;
	display: inline-block;
}

.litespeed-postbox.litespeed-postbox-partner a:hover {
	text-decoration: underline;
}

.litespeed-postbox.litespeed-postbox-partner img {
	max-width: 12rem;
}

.litespeed-dashboard-group .litespeed-postbox {
	width: calc(25% - 20px);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.litespeed-dashboard-group .litespeed-postbox-double {
	min-width: calc(50% - 20px);
	display: flex;
	justify-content: space-between;
}

.litespeed-postbox-double-content {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
}

.litespeed-postbox-double-content .litespeed-postbox-double-col {
	width: 50%;
}

.litespeed-postbox-double-content .litespeed-postbox-double-col:nth-child(2) {
	padding-left: 10px;
}

.litespeed-dashboard-group hr {
	margin: 1.5rem 0 0.75rem 0;
}

.litespeed-postbox .litespeed-postbox-refresh {
	text-decoration: none;
	color: #36b0b0;
	line-height: 1;
	vertical-align: top;
	margin-left: 0.5rem;
	margin-bottom: 0;
}

.litespeed-postbox .litespeed-postbox-refresh.button .dashicons {
	font-size: 22px;
	top: 0.05em;
}

.litespeed-postbox p:last-child {
	margin-bottom: 0;
}

.litespeed-label-dashboard {
	font-size: 0.92em;
	padding: 0.3em 0.6em 0.35em 0.6em;
	font-weight: normal;
	display: inline-block;
	margin-left: 8px;
	min-width: 2em;
}

.litespeed-label-dashboard:first-child {
	margin-left: 0;
	margin-right: 0.35em;
}

.litespeed-postbox .inside {
	padding: 0 20px 5px;
}

.litespeed-postbox .inside .litespeed-title {
	margin: 0 -20px 12px -20px;
	padding: 0px 20px 7px 20px;
	border-bottom: 1px solid #eee;
	font-size: 1.2em;
}

.litespeed-postbox .inside.litespeed-postbox-footer {
	border-top: 1px solid #efefef;
	background: #f9f9f9;
	padding: 20px;
	margin-bottom: 0px;
	margin-top: 0;
}

.litespeed-postbox-footer a,
a.litespeed-redetect {
	text-decoration: none;
}

.litespeed-postbox .inside.litespeed-postbox-footer--compact {
	padding: 7px 15px 8px 15px;
	font-size: 12px;
}

.litespeed-postbox-imgopt .litespeed-pie {
	width: 55px;
	height: 55px;
}

.litespeed-postbox-imgopt .litespeed-flex-container {
	align-items: center;
	margin-bottom: 10px;
}

.litespeed-postbox-imgopt .litespeed-flex-container .litespeed-icon-vertical-middle + div h3 {
	margin-top: 0;
}

.litespeed-postbox-imgopt .litespeed-flex-container .litespeed-icon-vertical-middle + div p {
	line-height: 1.2;
}

.litespeed-postbox-imgopt .litespeed-postbox-double-col:last-child > *:first-child {
	margin-top: 7px;
}

.litespeed-postbox-pagespeed p:first-child {
	margin-top: 0;
	margin-bottom: 0;
}

.litespeed-postbox-score-improve {
	line-height: 45px;
	margin-top: 7px;
	font-size: 42px;
}

.litespeed-postbox-pagespeed .litespeed-padding-space:first-child {
	padding-left: 5px;
	padding-right: 5px;
}

.litespeed-link-with-icon {
	text-decoration: underline;
	margin-right: 0.25em;
}

.litespeed-link-with-icon .dashicons {
	vertical-align: baseline;
	position: relative;
	top: 0.1em;
	font-size: 1em;
	text-decoration: none;
	width: auto;
	margin-right: 0.5em;
}

.litespeed-link-with-icon.litespeed-icon-right .dashicons {
	margin-left: 0.5em;
	margin-right: 0;
}

.litespeed-warning-bg {
	background-color: #b58a09 !important;
	color: white;
}

.litespeed-links-group:not(:last-child) {
	margin-bottom: 1em;
}

.litespeed-links-group > span:not(:last-child):after {
	content: '|';
	margin: 0 10px;
	color: #ddd;
	font-size: 13px;
}

.litespeed-wrap p.litespeed-qc-dashboard-link {
	margin-left: 1rem;
}

.litespeed-right.litespeed-qc-dashboard-link .dashicons {
	margin-left: 0.5em;
	margin-right: 0;
}

.litespeed-score-col {
	flex-grow: 1;
	padding-right: 15px;
}

.litespeed-score-col .litespeed-text-md {
	font-size: 1.35rem;
}

.litespeed-score-col.litespeed-score-col--imp {
	text-align: right;
	padding-right: 0;
}

.litespeed-score-col--imp .litespeed-text-jumbo {
	line-height: 1;
}

.litespeed-wrap span[data-balloon-pos] {
	border-bottom: 1px dashed;
}

.litespeed-wrap span[aria-label][data-balloon-pos] {
	cursor: default;
}

.litespeed-postbox--quiccloud {
	border-color: #253545;
}

.litespeed-postbox--quiccloud.litespeed-postbox .inside .litespeed-title {
	background: #253545;
	color: #e2e4e5;
	margin-top: -11px;
	padding: 10px 15px;
	margin-left: -15px;
	margin-right: -15px;
}

.litespeed-postbox--quiccloud.litespeed-postbox .inside .litespeed-title a {
	color: #8abff8;
}

.litespeed-postbox--quiccloud.litespeed-postbox .inside .litespeed-title a:hover {
	color: #a5caf2;
}

.litespeed-overwrite{
	display: inline-block;
	margin-left: 10px;
}

@media screen and (min-width: 1401px) {
	.litespeed-postbox--quiccloud.litespeed-postbox .inside .litespeed-title {
		padding-left: 20px;
		padding-right: 20px;
		margin-left: -20px;
		margin-right: -20px;
	}

	.litespeed-postbox .inside.litespeed-postbox-footer--compact {
		padding-left: 20px;
		padding-right: 20px;
	}
}

@media screen and (max-width: 1400px) and (min-width: 1024px) {
	.litespeed-dashboard-stats-wrapper .litespeed-postbox {
		flex-grow: 1;
	}

	.litespeed-postbox .inside {
		padding: 0 15px 5px;
	}

	.litespeed-dashboard-group .litespeed-postbox {
		width: calc(33.3333% - 20px);
	}

	.litespeed-dashboard-group .litespeed-postbox-double {
		min-width: calc(66.6666% - 20px);
	}
}

@media screen and (max-width: 1023px) {
	.litespeed-dashboard-stats-wrapper {
		flex-wrap: wrap;
	}

	.litespeed-dashboard-stats-wrapper .litespeed-postbox:not(:first-child) {
		border-left-color: #ccd0d4;
	}

	.litespeed-dashboard-stats-wrapper .litespeed-postbox {
		margin-top: -1px;
		min-width: calc(33.3333% - 1px);
	}

	.litespeed-postbox .inside {
		padding: 0 15px 5px;
	}

	.litespeed-dashboard-group .litespeed-postbox {
		width: calc(50% - 20px);
	}

	.litespeed-dashboard-group .litespeed-postbox-double {
		min-width: calc(100% - 20px);
	}
}

@media screen and (max-width: 719px) and (min-width: 480px) {
	.litespeed-dashboard-stats-wrapper .litespeed-postbox {
		margin-top: -1px;
		min-width: calc(50% - 2px);
	}
}

@media screen and (max-width: 569px) {
	.litespeed-dashboard-stats-wrapper .litespeed-postbox {
		min-width: 100%;
	}

	.litespeed-dashboard-group .litespeed-postbox {
		width: 100%;
	}

	.litespeed-postbox-double-content .litespeed-postbox-double-col {
		width: 100%;
	}

	.litespeed-postbox-double-content .litespeed-postbox-double-col:nth-child(2) {
		padding-left: 0;
		margin-top: 7px;
	}

	.litespeed-postbox-double-content {
		flex-wrap: wrap;
	}
}

/* =======================================
			VIEW - dashboard QC services
======================================= */

.litespeed-dashboard-qc {
	position: relative;
}

.litespeed-dashboard-unlock {
	text-align: center;
	background-color: #fff;
	box-shadow:
		0 0.125rem 0.4rem -0.0625rem rgba(0, 0, 0, 0.03),
		0px 3px 0px 0px rgba(0, 0, 0, 0.07);
	border-radius: 0.5rem;
	padding: 2rem;
	position: absolute;
	z-index: 5;
	left: 50%;
	transform: translate(-50%, 25%);
	top: 0;
	max-width: 96%;
	width: 540px;
}

.litespeed-dashboard-unlock.litespeed-dashboard-unlock--inline {
	position: relative;
	left: 50%;
	transform: translate(-50%, 0);
	border: 1px solid #e5e5e5;
	background: #fafafa;
	margin-top: 2rem;
	margin-bottom: 1rem;
	max-width: calc(100% - 4rem);
}

.litespeed-dashboard-unlock-title {
	font-size: 28px;
}

.litespeed-dashboard-unlock-desc {
	font-size: 17px;
	color: #000;
}

.litespeed-dashboard-unlock-desc span {
	font-size: 14px;
	color: #666;
}

p.litespeed-dashboard-unlock-footer {
	margin: 3em auto 0 auto;
	max-width: 500px;
}

.litespeed-qc-text-gradient {
	background: -webkit-linear-gradient(130deg, #ff2a91, #2295d8 60%, #161f29);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	font-weight: 800;
}

.litespeed-dashboard-unlock a.button.button-primary,
.litespeed-wrap .button.litespeed-button-cta {
	font-size: 1.2em;
	padding: 0.35em 1em 0.35em 0.85em;
	min-width: 210px;
	text-align: center;
}

.litespeed-dashboard-unlock a.button.button-primary {
	margin-top: 10px;
}

.litespeed-dashboard-unlock a.button.button-primary .dashicons,
.litespeed-wrap .button.litespeed-button-cta .dashicons {
	vertical-align: baseline;
	top: 0.25em;
	margin-right: 0.5em;
}

.litespeed-dashboard-unlock + .litespeed-dashboard-qc-enable {
	opacity: 0.75;
	filter: blur(2px);
}

.litespeed-dashboard-unlock + .litespeed-dashboard-qc-enable:before {
	content: '';
	position: absolute;
	left: -10px;
	top: -5px;
	width: calc(100% + 20px);
	height: calc(100% + 10px);
	background: #161e29;
	z-index: 2;
	opacity: 0.55;
	filter: blur(2px);
}

@media screen and (min-width: 1400px) {
	.litespeed-dashboard-unlock {
		width: 800px;
	}
}

@media screen and (max-width: 640px) {
	.litespeed-dashboard-unlock {
		max-width: 80%;
		padding: 1rem 1.5rem 2rem 1.5rem;
		transform: translate(-50%, 10%);
	}

	.litespeed-dashboard-unlock-title {
		font-size: 22px;
		line-height: 1.2;
	}
}

@media screen and (max-width: 340px) {
	.litespeed-dashboard-unlock a.button.button-primary,
	.litespeed-wrap .button.litespeed-button-cta {
		padding: 0.35em 1em 0.35em 1em;
	}

	.litespeed-dashboard-unlock a.button.button-primary .dashicons,
	.litespeed-wrap .button.litespeed-button-cta .dashicons {
		display: none;
	}

	p.litespeed-dashboard-unlock-footer {
		margin-top: 2em;
	}
}

/********************************* todo *******************************/

/* image optimize page */

.litespeed-column-java {
	background: #5cadad !important;
}

.litespeed-text-shipgrey {
	color: #535342 !important;
}

.litespeed-text-dimgray {
	color: #666666 !important;
}

.litespeed-text-grey {
	color: #999999 !important;
}

.litespeed-text-whisper {
	color: #e6e6e6 !important;
}

.litespeed-text-malibu {
	color: #5cbdde !important;
}

.litespeed-text-morningglory {
	color: #99cccc !important;
}

.litespeed-text-fern {
	color: #66cc66 !important;
}

.litespeed-text-persiangreen {
	color: #009999 !important;
}

.litespeed-text-lead {
	font-size: 16px;
}

.litespeed-text-small {
	font-size: 12px;
	line-height: 14px;
}

.litespeed-text-thin {
	font-weight: 100;
}

.litespeed-contrast {
	color: white;
}

.litespeed-hr-dotted {
	border: 1px dotted #eeeeee;
}

.litespeed-hr {
	padding-bottom: 1.5em;
	border-bottom: 0.5px solid #97caca;
}

.litespeed-hr-with-space {
	border-top: 1px solid #eeeeee;
	margin: 2em 0;
	border-bottom: none;
}

.litespeed-icon-vertical-middle {
	vertical-align: middle;
	display: inline-block;
	margin: 0px 10px 0px 10px;
}

.litespeed-column-java .litespeed-danger {
	color: #c1c53a !important;
}

.litespeed-column-java .litespeed-desc {
	color: #bfbfbf;
}

.litespeed-column-java code {
	color: #c2f5bf;
	background-color: #238888;
}

.litespeed-column-java .litespeed-title {
	color: white;
}

.litespeed-width-7-10 .litespeed-progress {
	margin: 1em;
}

.litespeed-refresh:after {
	content: '⟳';
	width: 20px;
	height: 20px;
	color: #40ad3a;
}

.litespeed-column-java .litespeed-refresh:after {
	color: #23ec17;
}

.litespeed-refresh:hover:after,
.litespeed-refresh:focus:after,
.litespeed-refresh:focus:active:after {
	color: #7ffbfb;
}

.litespeed-width-3-10 .litespeed-title {
	margin: 18px 0;
}

.litespeed-silence {
	color: #b1b1b1;
}

.litespeed-column-java .litespeed-congratulate {
	color: #c2f5bf;
	font-size: 20px;
}

.litespeed-light-code .litespeed-silence code {
	background-color: #f0f5fb;
}

.litespeed-column-java .litespeed-btn-danger {
	color: #f194a8;
	border-color: #f194a8;
}

.litespeed-column-java .litespeed-btn-danger:hover {
	background: #f194a8;
}

.litespeed-column-java svg.litespeed-pie circle.litespeed-pie_bg {
	stroke: #e8efe7;
}

.litespeed-column-java svg.litespeed-pie circle.litespeed-pie_circle {
	stroke: #97caca;
}

.litespeed-column-java svg .litespeed-pie_info text {
	fill: #f5ffeb;
}

.litespeed-column-java svg g.litespeed-pie_info .litespeed-pie-done {
	fill: #a5ffa0;
}

.litespeed-column-java a {
	color: #eaf8ff;
}

.litespeed-column-java a:hover {
	color: #ffffff;
}

.litespeed-progress-bar-blue {
	background-color: #33adff;
}

.litespeed-status-current {
	font-size: 3.5em;
	margin: 1.25em 0em 0.75em 0em;
}

/* .litespeed-title, .litespeed-title-short {
	margin: 18px 0;
	border-bottom: 1px solid #C1D5EA;
	margin: 2.5em 0px 1.5em 0 !important;
} */

.litespeed-column-java .litespeed-desc {
	color: #cae4e4;
}

.litespeed-column-java .litespeed-warning {
	color: #ffd597 !important;
}

.litespeed-column-java .litespeed-btn-success {
	color: #ddf1e4;
	border: 1px solid #33ad5c;
	background: #33ad5c;
}

.litespeed-column-java .litespeed-btn-success:hover {
	color: #ffffff;
	border: 1px solid #7dca97;
	background: #009933;
}

.litespeed-column-java .litespeed-btn-warning {
	color: #fff1dd;
	border: 1px solid #ff9933;
	background-color: #ff9933;
}

.litespeed-column-java .litespeed-btn-warning:hover {
	color: #ffffff;
	border-color: #ffca7d;
	background: #ff9900;
}

.litespeed-column-java .litespeed-btn-danger {
	color: #ffeadd !important;
	border: 1px solid #ff6600 !important;
	background: #ff5c5c;
}

.litespeed-column-java .litespeed-btn-danger:hover {
	color: #ffffff;
	border: 1px solid #ff9797 !important;
	background: #ff0000;
}

.litespeed-column-java .litepseed-dash-icon-success,
.litepseed-dash-icon-success {
	color: #5cdede;
	font-size: 2em;
	margin-top: -0.25em;
}

.litespeed-column-java .litepseed-dash-icon-success:hover,
.litepseed-dash-icon-success:hover {
	color: #7de5e5;
}

.litespeed-dashicons-large {
	font-size: 2em;
}

.litespeed-column-java p {
	color: #ffffff;
}

.litespeed-body tbody > tr > th.litespeed-padding-left {
	padding-left: 3em;
}

@media screen and (max-width: 680px) {
	.litespeed-body tbody > tr > th.litespeed-padding-left {
		padding-left: 10px;
	}

	.litespeed-body tbody > tr > th.litespeed-padding-left:before {
		content: '\2014\2014';
		color: #ccc;
		margin-right: 5px;
	}
}

.litespeed-txt-small {
	font-size: 12px;
}

.litespeed-txt-disabled .litespeed-text-dimgray {
	color: #aaaaaa;
}

.litespeed-txt-disabled svg {
	fill: #aaaaaa;
}

.litespeed-txt-disabled circle.litespeed-pie_circle {
	stroke: #cccccc;
}

.litespeed-txt-disabled g.litespeed-pie_info text {
	color: #cccccc;
}

a.litespeed-media-href svg:hover {
	border-radius: 50%;
	background: #f1fcff;
	fill: #5ccad7;
	box-shadow: 0 0 5px 1px #7dd5df;
	transition: all 0.2s ease-out;
	transform: scale(1.05);
}

.litespeed-media-p a .dashicons-trash {
	font-size: 2.25em;
	vertical-align: middle;
	display: inline;
	border-radius: 50%;
	line-height: 1.5em;
}

.litespeed-media-p a .dashicons-trash:hover {
	transition: all 0.2s ease-out;
	color: #ffa500 !important;
	background: #fff5e6;
	box-shadow: 0 0 10px 1px #ff8c00;
}

.litespeed-media-p div > svg circle.litespeed-pie_bg {
	stroke: #ecf2f9;
}

.litespeed-media-p div > svg circle.litespeed-pie_circle {
	stroke: #9fbfdf;
}

.litespeed-media-p div > svg {
	fill: #538cc6;
	background: rgba(236, 242, 249, 0.1);
	border-radius: 50%;
}

.litespeed-banner-description-padding-right-15 {
	padding-right: 15px;
}

.litespeed-banner-description {
	display: inline-flex;
	flex-wrap: wrap;
}

.litespeed-banner-description-content {
	margin: 0px;
	line-height: 1.25em;
}

.litespeed-banner-button-link {
	white-space: nowrap;
	margin: 0px;
	line-height: 1.5em;
	padding-bottom: 5px;
}

.litespeed-notice-dismiss {
	position: absolute;
	right: 25px;
	border: none;
	margin: 0;
	padding: 10px;
	background: none;
	cursor: pointer;
	color: #888888;
	display: block;
	height: 20px;
	text-align: center;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-weight: 600;
	text-decoration: none;
}

.litespeed-notice-dismiss:hover,
.litespeed-notice-dismiss:active,
.litespeed-notice-dismiss:focus {
	color: #cc2929;
}

.litespeed-dot {
	display: inline-block;
	border-radius: 50%;
	width: 20px;
	height: 20px;
	color: white;
	text-align: center;
}

.litespeed-badge {
	display: inline-block;
	border-radius: 20%;
	min-width: 50px;
	height: 20px;
	color: white;
	text-align: center;
}

/* =======================================
	Comparison Cards - Presets
======================================= */

.litespeed-comparison-card {
	box-sizing: border-box;
}

.litespeed-comparison-card-rec .litespeed-card-content > div.litespeed-card-body {
	font-size: 14px;
}

.litespeed-comparison-card-rec .litespeed-card-action {
	margin-bottom: 0.25rem;
}

.litespeed-comparison-card-rec h3 {
	font-size: 20px;
}

.litespeed-card-content > div,
.litespeed-card-action {
	padding: 0.85rem 1.25rem;
}

.litespeed-card-header {
	border-bottom: 1px solid #eee;
	background: #f9fafc;
}

.litespeed-card-content > div.litespeed-card-body {
	align-self: stretch;
	justify-content: flex-end;
	font-size: 15px;
	padding-bottom: 0.5rem;
	padding-top: 1rem;
}

.litespeed-card-content > div.litespeed-card-footer {
	align-self: stretch;
	justify-content: flex-end;
	padding-bottom: 0;
	padding-top: 0.25rem;
}

.litespeed-card-action {
	justify-content: flex-end;
}

.litespeed-comparison-card ul {
	padding-left: 20px;
	list-style: none;
	list-style-position: outside;
	margin: 0;
}

.litespeed-comparison-card li {
	margin-bottom: 0.5em;
	line-height: 1.4;
}

.litespeed-comparison-card li:last-child {
	margin-bottom: 0;
}

.litespeed-comparison-card ul li:before {
	content: '✓';
	margin-left: -1em;
	margin-right: 0.35em;
	color: #329c74;
}

@media screen and (max-width: 1279px) {
	.litespeed-comparison-card {
		margin: 0 0 -1px 0;
	}
}

@media screen and (min-width: 640px) and (max-width: 1279px) {
	.litespeed-comparison-cards {
		max-width: 740px;
	}

	.litespeed-card-content {
		display: flex;
		flex-wrap: wrap;
	}

	.litespeed-card-content .litespeed-card-header {
		width: 100%;
	}

	.litespeed-card-content > div.litespeed-card-body {
		align-self: initial;
		width: 50%;
		box-sizing: border-box;
	}

	.litespeed-card-content > div.litespeed-card-footer {
		width: 50%;
		align-self: initial;
		box-sizing: border-box;
	}

	.litespeed-card-content > div.litespeed-card-footer h4 {
		margin-top: 1rem;
	}
}

@media screen and (min-width: 1280px) {
	.litespeed-comparison-cards {
		display: flex;
		margin: 3rem 0 2rem 0;
		max-width: 1720px;
	}

	.litespeed-comparison-card {
		width: 19%;
		min-width: 0;
		display: flex;
		flex-direction: column;
		margin-right: -1px;
		justify-content: space-between;
	}

	.litespeed-comparison-card:first-child {
		border-top-left-radius: 5px;
		border-bottom-left-radius: 5px;
		overflow: hidden;
	}

	.litespeed-comparison-card:last-child {
		border-top-right-radius: 5px;
		border-bottom-right-radius: 5px;
		overflow: hidden;
	}

	.litespeed-comparison-card-rec {
		width: 23%;
		padding-top: 1rem;
		padding-bottom: 0.75rem;
		margin-top: -1rem;
		margin-bottom: 0.25rem;
		border-radius: 5px;
		overflow: hidden;
	}

	.litespeed-comparison-card-rec .litespeed-card-header {
		margin-top: -1rem;
		padding-top: 1.75rem;
		padding-bottom: 0.95rem;
	}
}

/* =======================================
		BALLOON PURE CSS TOOLTIPS
======================================= */

.litespeed-wrap {
	--balloon-color: rgba(16, 16, 16, 0.95);
	--balloon-font-size: 12px;
	--balloon-move: 4px;
}

.litespeed-wrap button[aria-label][data-balloon-pos] {
	overflow: visible;
}

.litespeed-wrap [aria-label][data-balloon-pos] {
	position: relative;
	cursor: pointer;
}

.litespeed-wrap [aria-label][data-balloon-pos]:after {
	opacity: 0;
	pointer-events: none;
	transition: all 0.2s ease 0.05s;
	text-indent: 0;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	font-weight: normal;
	font-style: normal;
	text-shadow: none;
	font-size: var(--balloon-font-size);
	background: var(--balloon-color);
	border-radius: 2px;
	color: #fff;
	content: attr(aria-label);
	padding: 0.5em 1em;
	position: absolute;
	white-space: nowrap;
	z-index: 10;
	line-height: 1.4;
}

.litespeed-wrap [aria-label][data-balloon-pos]:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-top-color: var(--balloon-color);
	opacity: 0;
	pointer-events: none;
	transition: all 0.2s ease 0.05s;
	content: '';
	position: absolute;
	z-index: 10;
}

.litespeed-wrap [aria-label][data-balloon-pos]:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos]:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-visible]:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-visible]:after,
.litespeed-wrap [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:before,
.litespeed-wrap [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:after {
	opacity: 1;
	pointer-events: none;
}

.litespeed-wrap [aria-label][data-balloon-pos].font-awesome:after {
	font-family:
		FontAwesome,
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		Oxygen,
		Ubuntu,
		Cantarell,
		'Open Sans',
		'Helvetica Neue',
		sans-serif;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-break]:after {
	white-space: pre;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-break][data-balloon-length]:after {
	white-space: pre-line;
	word-break: break-word;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-blunt]:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-blunt]:after {
	transition: none;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up']:after {
	bottom: 100%;
	left: 50%;
	margin-bottom: 10px;
	transform: translate(-50%, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up']:before {
	bottom: 100%;
	left: 50%;
	transform: translate(-50%, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up'][data-balloon-visible]:after {
	transform: translate(-50%, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up'][data-balloon-visible]:before {
	transform: translate(-50%, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left']:after {
	bottom: 100%;
	left: 0;
	margin-bottom: 10px;
	transform: translate(0, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left']:before {
	bottom: 100%;
	left: 5px;
	transform: translate(0, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left'][data-balloon-visible]:after {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-left'][data-balloon-visible]:before {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right']:after {
	bottom: 100%;
	right: 0;
	margin-bottom: 10px;
	transform: translate(0, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right']:before {
	bottom: 100%;
	right: 5px;
	transform: translate(0, var(--balloon-move));
	transform-origin: top;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right'][data-balloon-visible]:after {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='up-right'][data-balloon-visible]:before {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down']:after {
	left: 50%;
	margin-top: 10px;
	top: 100%;
	transform: translate(-50%, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down']:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-bottom-color: var(--balloon-color);
	left: 50%;
	top: 100%;
	transform: translate(-50%, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down'][data-balloon-visible]:after {
	transform: translate(-50%, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down'][data-balloon-visible]:before {
	transform: translate(-50%, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left']:after {
	left: 0;
	margin-top: 10px;
	top: 100%;
	transform: translate(0, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left']:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-bottom-color: var(--balloon-color);
	left: 5px;
	top: 100%;
	transform: translate(0, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left'][data-balloon-visible]:after {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-left'][data-balloon-visible]:before {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right']:after {
	right: 0;
	margin-top: 10px;
	top: 100%;
	transform: translate(0, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right']:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-bottom-color: var(--balloon-color);
	right: 5px;
	top: 100%;
	transform: translate(0, calc(var(--balloon-move) * -1));
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right'][data-balloon-visible]:after {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='down-right'][data-balloon-visible]:before {
	transform: translate(0, 0);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left']:after {
	margin-right: 10px;
	right: 100%;
	top: 50%;
	transform: translate(var(--balloon-move), -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left']:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-left-color: var(--balloon-color);
	right: 100%;
	top: 50%;
	transform: translate(var(--balloon-move), -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left'][data-balloon-visible]:after {
	transform: translate(0, -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='left'][data-balloon-visible]:before {
	transform: translate(0, -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right']:after {
	left: 100%;
	margin-left: 10px;
	top: 50%;
	transform: translate(calc(var(--balloon-move) * -1), -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right']:before {
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-right-color: var(--balloon-color);
	left: 100%;
	top: 50%;
	transform: translate(calc(var(--balloon-move) * -1), -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right']:hover:after,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right'][data-balloon-visible]:after {
	transform: translate(0, -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right']:hover:before,
.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-pos='right'][data-balloon-visible]:before {
	transform: translate(0, -50%);
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='small']:after {
	white-space: normal;
	width: 80px;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='medium']:after {
	white-space: normal;
	width: 150px;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='large']:after {
	white-space: normal;
	width: 260px;
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='xlarge']:after {
	white-space: normal;
	width: 380px;
}

@media screen and (max-width: 768px) {
	.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='xlarge']:after {
		white-space: normal;
		width: 90vw;
	}
}

.litespeed-wrap [aria-label][data-balloon-pos][data-balloon-length='fit']:after {
	white-space: normal;
	width: 100%;
}

/* =======================================
		Misc Mobile TWEAKS
======================================= */

@media screen and (max-width: 680px) {
	.litespeed-wrap .litespeed-body .field-col {
		margin-left: 0;
	}

	.litespeed-width-auto.litespeed-table-compact td {
		font-size: 12px;
		word-break: break-word;
	}

	input#input_api_key + .button {
		margin-top: 10px;
		margin-left: 0;
	}

	input#input_api_key + .button + .litespeed-desc {
		display: block;
	}

	input#input_api_key + .button + .litespeed-desc + .button {
		margin-left: 0;
	}

	.litespeed-body .litespeed-table td .litespeed-right {
		float: none !important;
	}

	.litespeed-title a.litespeed-learn-more,
	.litespeed-title-short a.litespeed-learn-more {
		display: block;
		margin-left: 0;
		margin-top: 5px;
	}
}

.litespeed-wrap .litespeed-redetect[aria-label][data-balloon-pos][data-balloon-pos='up']:after {
	left: auto;
	right: 0;
	transform: translate(0%, var(--balloon-move));
}

.litespeed-wrap .litespeed-redetect[aria-label][data-balloon-pos][data-balloon-pos='up']:hover:after,
.litespeed-wrap .litespeed-redetect[aria-label][data-balloon-pos][data-balloon-pos='up'][data-balloon-visible]:after {
	transform: translate(0, 0);
}

/* =======================================
					QC
======================================= */

.litespeed-col-status-data h3,
.litespeed-col-status-data h4 {
	margin-bottom: 0;
	margin-top: 20px;
}

.litespeed-col-status-data h3 .dashicons {
	vertical-align: bottom;
}

.litespeed-col-status-data h4 .dashicons {
	vertical-align: sub;
}

/* To use on dark bg */
.litespeed-wrap .litespeed-qc-button {
	background-color: #5efffc;
	border: 1px solid #00d0cb;
	box-shadow: 0px 2px 0px 0px #00d0cb;
	color: #161f29;
	font-weight: 600;
	font-size: 15px;
	padding: 12px 24px;
	border-radius: 3px;
	line-height: 1;
	display: inline-flex;
	align-items: center;
	transition: 0.25s;
}

.litespeed-wrap .litespeed-qc-button:hover {
	background: #21a29f21;
	color: #5efffc;
	border-color: #00d0cb;
}

.litespeed-wrap .litespeed-qc-button .dashicons {
	top: auto;
}

.litespeed-postbox.litespeed-qc-promo-box {
	background: #161e29 linear-gradient(110deg, #171c2fbd, #252766ab);
	border-radius: 5px;
	box-shadow: 0px 4px 0px 0px #161d2e;
	border: none;
}

.litespeed-postbox.litespeed-qc-promo-box .inside {
	padding: 25px;
	margin: 0;
}

.litespeed-dashboard-group .litespeed-postbox.litespeed-qc-promo-box {
	box-shadow: none;
}

.litespeed-dashboard-group .litespeed-postbox.litespeed-qc-promo-box .inside {
	padding: 20px 25px;
}

.litespeed-postbox.litespeed-qc-promo-box h3 {
	margin-top: 0;
	color: #fff;
	font-size: 24px;
	font-weight: 800;
	line-height: 1.4em;
}

.litespeed-postbox.litespeed-qc-promo-box h3 .litespeed-quic-icon {
	width: 24px;
	height: 28px;
	background-size: contain;
	margin-right: 10px;
}

.litespeed-postbox.litespeed-qc-promo-box p {
	color: #dbdbdb;
	font-size: 1rem;
}

/* =======================================
	   Deactivate modal
======================================= */
#litespeed-modal-deactivate {
	padding: 20px;
}

#litespeed-modal-deactivate h2 {
	margin: 0px;
}

#litespeed-modal-deactivate .litespeed-wrap {
	margin: 10px 0px;
}

#litespeed-modal-deactivate .deactivate-clear-settings-wrapper,
#litespeed-modal-deactivate .deactivate-actions {
	margin-top: 30px;
}

#litespeed-modal-deactivate .deactivate-reason-wrapper label,
#litespeed-modal-deactivate .deactivate-clear-settings-wrapper label {
	width: 100%;
	display: block;
	margin-bottom: 5px;
}

#litespeed-modal-deactivate .deactivate-actions {
	display: flex;
	justify-content: space-between;
}