<?php

add_action('acf/include_fields', function () {

    
    $title = 'contacts';
    $message = 'Block displays contact information from theme settings. (Theme settings -> <a href="/wp-admin/admin.php?page=contacts-settings" target="_blank">Contacts</a>)';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'контакты';
        $message = 'Блок выводит контактную информацию из настроек. (Настройки темы -> <a href="/wp-admin/admin.php?page=contacts-settings" target="_blank">Контакты</a>)';
    }

    acf_add_local_field_group([
        'key' => 'contacts',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_contacts_message',
                'label' => $title,
                'name' => 'contacts_message',
                'aria-label' => '',
                'type' => 'message',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'message' => $message,
                'new_lines' => 'br',
                'esc_html' => 0,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/contacts',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});