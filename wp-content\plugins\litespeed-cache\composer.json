{"name": "litespeedtech/lscache_wp", "require-dev": {"squizlabs/php_codesniffer": "^3.12", "phpcompatibility/php-compatibility": "*", "wp-coding-standards/wpcs": "^3.1", "phpcsstandards/phpcsutils": "^1.0", "phpcsstandards/phpcsextra": "^1.2", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "php-stubs/wp-cli-stubs": "^2.12"}, "prefer-stable": true, "scripts": {"sniff-check": "vendor/bin/phpcs --standard=phpcs.ruleset.xml --no-cache cli/ lib/ src/ tpl/ thirdparty autoload.php litespeed-cache.php"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}