{"packages": [{"name": "enshrined/svg-sanitize", "version": "0.22.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "0afa95ea74be155a7bcd6c6fb60c276c39984500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/0afa95ea74be155a7bcd6c6fb60c276c39984500", "reference": "0afa95ea74be155a7bcd6c6fb60c276c39984500", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "time": "2025-08-12T10:13:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.22.0"}, "install-path": "../enshrined/svg-sanitize"}], "dev": true, "dev-package-names": []}