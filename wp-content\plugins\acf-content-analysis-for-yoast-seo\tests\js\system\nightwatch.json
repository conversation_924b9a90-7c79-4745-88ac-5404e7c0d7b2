{"src_folders": ["tests"], "output_folder": "reports", "custom_commands_path": "", "custom_assertions_path": "", "page_objects_path": "pages", "globals_path": ".env.js", "selenium": {"start_process": false}, "test_settings": {"default": {"launch_url": "http://local.wordpress.dev", "globals": {}, "screenshots": {"enabled": true, "on_failure": true, "on_error": false, "path": "screenshots"}, "selenium_port": 9515, "selenium_host": "localhost", "default_path_prefix": "", "desiredCapabilities": {"loggingPrefs": {"browser": "ALL"}, "browserName": "chrome", "chromeOptions": {"args": ["--headless", "--disable-gpu", "--incognito", "--window-size=1280,4048"]}}}}}