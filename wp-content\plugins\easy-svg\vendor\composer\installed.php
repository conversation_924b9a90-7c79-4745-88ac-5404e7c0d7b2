<?php return array(
    'root' => array(
        'name' => 'benjaminzekavica/easy-svg',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'f2477c7744d8c44c69f347357b146b9adcc4dca7',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'benjaminzekavica/easy-svg' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'f2477c7744d8c44c69f347357b146b9adcc4dca7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'enshrined/svg-sanitize' => array(
            'pretty_version' => '0.22.0',
            'version' => '0.22.0.0',
            'reference' => '0afa95ea74be155a7bcd6c6fb60c276c39984500',
            'type' => 'library',
            'install_path' => __DIR__ . '/../enshrined/svg-sanitize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
