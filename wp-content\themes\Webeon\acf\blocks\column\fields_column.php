<?php

add_action('acf/include_fields', function () {

    
    $title = 'Column';
    $message = 'Fills part of the width. Can contain different blocks.';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Колонка';
        $message = 'Занимает часть ширины. Может содержать различные блоки.';
    }

    acf_add_local_field_group([
        'key' => 'column',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_column_message',
                'label' => $title,
                'name' => 'column_message',
                'aria-label' => '',
                'type' => 'message',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'message' => $message,
                'new_lines' => 'br',
                'esc_html' => 0,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/column',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});