<?php

add_action('customize_register', function ($wp_customize) {
    //lang
    $logoLabel = 'Upload Logo';
    $sectionThemeColors = 'Theme Colors';
    if (ADMIN_LOCALE == 'ru_RU') {
        $logoLabel = 'Загрузить Логотип';
        $sectionThemeColors = 'Цвета темы';
    }

    //sections
    $wp_customize->add_section( 'webeon_colors', [
        'title'       => __( $sectionThemeColors, 'webeon' ),
        'description' => __( '', 'webeon' ),
        'priority'    => 30,
    ] );

    // add a setting for the site logo
    $wp_customize->add_setting('themeLogo');
    // Add a control to upload the logo
    $wp_customize->add_control(new WP_Customize_Media_Control(
        $wp_customize,
        'themeLogo',
        [
            'label' => $logoLabel,
            'section' => 'title_tagline',
            'settings' => 'themeLogo',
            'mime_type' => 'image',
        ]
    ));
});