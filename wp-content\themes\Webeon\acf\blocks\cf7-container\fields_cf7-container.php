<?php

add_action('acf/include_fields', function () {

    
    $title = 'Form styling';
    $titleType = 'Display type';
    $titleTypeDefault = 'In a box';
    $titleTypeLine = 'In a line';
    $titleHalfFields = 'Number of first fields with half width';
    $titleButtonPosition = 'Button position';
    $titleButtonPositionOn = 'In a line';
    $titleButtonPositionOff = 'Under the form';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Стилизация формы';
        $titleType = 'Тип отображения';
        $titleTypeDefault = 'В столбик';
        $titleTypeLine = 'В строку';
        $titleHalfFields = 'Количество первых полей с половинным шириной';
        $titleButtonPosition = 'Позиция кнопки';
        $titleButtonPositionOn = 'В линии';
        $titleButtonPositionOff = 'Под формой';
    }

    acf_add_local_field_group([
        'key' => 'cf7_container',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_cf7_container_group',
                'label' => $title,
                'name' => 'cf7_container_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_cf7_container_type',
                        'label' => $titleType,
                        'name' => 'cf7_container_type',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'default' => $titleTypeDefault,
                            'line' => $titleTypeLine,
                        ],
                        'default_value' => 'default',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cf7_container_half_fields',
                        'label' => $titleHalfFields,
                        'name' => 'cf7_container_half_fields',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => [
                            [
                                [
                                    'field' => 'field_cf7_container_type',
                                    'operator' => '==',
                                    'value' => 'default',
                                ],
                            ],
                        ],
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 0,
                        'min' => 0,
                        'max' => 4,
                        'allow_in_bindings' => 0,
                        'step' => 2,
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_cf7_container_button',
                        'label' => $titleButtonPosition,
                        'name' => 'cf7_container_button',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => [
                            [
                                [
                                    'field' => 'field_cf7_container_type',
                                    'operator' => '==',
                                    'value' => 'line',
                                ],
                            ],
                        ],
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'allow_in_bindings' => 0,
                        'ui_on_text' => $titleButtonPositionOn,
                        'ui_off_text' => $titleButtonPositionOff,
                        'ui' => 1,
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/cf7-container',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});