<?php
add_action('acf/include_fields', function () {

    
    $title = 'Hero Block';
    $titleImg = 'Image';
    $titleImgMobile = 'Image for mobile';
    $descriptionImgMobile = 'If not specified, the main image will be used.';
    $titleImgTab = 'Img';
    $titleHeader = 'Header (H1)';
    $titleHeaderLikeTitle = 'Text, continues header';
    $titleHeaderText = 'Text after header';
    $descriptionScroller = 'Use [text1, text2, text3] to create a scroller';
    $titleLink = 'Link';
    $titleMainTab = 'Text';
    $titleSettingsTab = 'Set.';
    $titleDescriptionTextSize = 'Description Text Size';
    $titleTextSize = 'Title Text Size';
    $titleDescriptionTextSizeBig = 'Big';
    $titleDescriptionTextSizeDefault = 'Default';
    $titleDescriptionTextSizeSmall = 'Small';
    $titleTextAlignCenter = 'Text align center';
    $titleMarginTop = 'Enable margin at the top';
    $titleMargin = 'Enable margin at the bottom';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Заглавный Блок';
        $titleImg = 'Картинка';
        $titleImgMobile = 'Картинка для мобильных устройств';
        $descriptionImgMobile = 'Если не указано, то используется основная картинка.';
        $titleImgTab = 'Изо.';
        $titleHeader = 'Заголовок (H1)';
        $titleHeaderLikeTitle = 'Текст, продолжает заголовок.';
        $titleHeaderText = 'Текст после заголовка';
        $descriptionScroller = 'Используйте [текст1, текст2, текст3] для создания скроллера';
        $titleLink = 'Ссылка';
        $titleMainTab = 'Текст';
        $titleSettingsTab = 'Настр.';
        $titleDescriptionTextSize = 'Размер текста описания';
        $titleTextSize = 'Размер текста заголовка';
        $titleDescriptionTextSizeBig = 'Большой';
        $titleDescriptionTextSizeDefault = 'Стандартный';
        $titleDescriptionTextSizeSmall = 'Маленький';
        $titleTextAlignCenter = 'Выравнивание текста по центру';
        $titleMarginTop = 'Включить отступ сверху';
        $titleMargin = 'Включить отступ снизу';
    }

    acf_add_local_field_group([
        'key' => 'hero',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_hero_group',
                'label' => $title,
                'name' => 'hero_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_hero_main_tab',
                        'label' => $titleMainTab,
                        'name' => 'hero_main_tab',
                        'aria-label' => '',
                        'type' => 'tab',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'placement' => 'top',
                        'endpoint' => 0,
                        'selected' => 1,
                    ],                    
                    [
                        'key' => 'field_hero_head',
                        'label' => $titleHeader,
                        'name' => 'hero_head',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => $titleHeader,
                        'maxlength' => '',
                        'rows' => 2,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_head_like_title',
                        'label' => $titleHeaderLikeTitle,
                        'name' => 'hero_head_like_title',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => $descriptionScroller,
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => $titleHeaderLikeTitle,
                        'maxlength' => '',
                        'rows' => 3,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_text',
                        'label' => $titleHeaderText,
                        'name' => 'hero_text',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => $descriptionScroller,
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'rows' => 3,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_link',
                        'label' => $titleLink,
                        'name' => 'hero_link',
                        'aria-label' => '',
                        'type' => 'link',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'array',
                    ],
                    [
                        'key' => 'field_hero_img_tab',
                        'label' => $titleImgTab,
                        'name' => 'hero_img_tab',
                        'aria-label' => '',
                        'type' => 'tab',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'placement' => 'top',
                        'endpoint' => 0,
                        'selected' => 0,
                    ],
                    [
                        'key' => 'field_hero_img',
                        'label' => $titleImg,
                        'name' => 'hero_img',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'thumbnail',
                    ],
                    [
                        'key' => 'field_hero_img_mobile',
                        'label' => $titleImgMobile,
                        'name' => 'hero_img_mobile',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => $descriptionImgMobile,
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'thumbnail',
                    ],
                    [
                        'key' => 'field_hero_settings_tab',
                        'label' => $titleSettingsTab,
                        'name' => 'hero_settings_tab',
                        'aria-label' => '',
                        'type' => 'tab',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'placement' => 'top',
                        'endpoint' => 0,
                        'selected' => 0,
                    ],
                    [
                        'key' => 'field_hero_size_title',
                        'label' => $titleTextSize,
                        'name' => 'hero_size_title',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'big' => $titleDescriptionTextSizeBig,
                            'default' => $titleDescriptionTextSizeDefault,
                            'small' => $titleDescriptionTextSizeSmall,
                        ],
                        'default_value' => 'default',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_hero_size_description',
                        'label' => $titleDescriptionTextSize,
                        'name' => 'hero_size_description',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'big' => $titleDescriptionTextSizeBig,
                            'default' => $titleDescriptionTextSizeDefault,
                            'small' => $titleDescriptionTextSizeSmall,
                        ],
                        'default_value' => 'default',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_hero_text_align_center',
                        'label' => $titleTextAlignCenter,
                        'name' => 'hero_text_align_center',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                    [
                        'key' => 'field_hero_margin_top',
                        'label' => $titleMarginTop,
                        'name' => 'hero_margin_top',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                    [
                        'key' => 'field_hero_margin',
                        'label' => $titleMargin,
                        'name' => 'hero_margin',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/hero',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});