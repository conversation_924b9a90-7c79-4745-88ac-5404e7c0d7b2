<?php

add_action('acf/include_fields', function () {

    
    $title = 'Category';
    $titleCategory = 'Choose category';
    $titleCount = 'Number of items to show';
    $titleNum = 'Number of items in row';
    $titleButton = 'Text of button to go to category';
    $defaultButton = 'View All';
    $titleAlignment = 'Alignment of cards titles';
    $titleTextAlignment = 'Alignment of cards text';
    $textAlignmentLeft = 'Left'; 
    $textAlignmentCenter = 'Center';    
    $textAlignmentRight = 'Right';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Категория';
        $titleCategory = 'Выберите категорию (рубрику)';
        $titleCount = 'Количество показываемых элементов';
        $titleNum = 'Количество элементов в ряд';
        $titleButton = 'Текст кнопки для перехода в категорию (рубрику)';
        $defaultButton = 'Посмотреть все';
        $titleAlignment = 'Выравнивание заголовков карточек';
        $titleTextAlignment = 'Выравнивание текста карточек';
        $textAlignmentLeft = 'Слева';
        $textAlignmentCenter = 'По центру';
        $textAlignmentRight = 'Справа';
    }

    acf_add_local_field_group([
        'key' => 'category',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_category_group',
                'label' => $title,
                'name' => 'category_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_category_choose',
                        'label' => $titleCategory,
                        'name' => 'category_choose',
                        'aria-label' => '',
                        'type' => 'taxonomy',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'taxonomy' => 'category',
                        'add_term' => 1,
                        'save_terms' => 0,
                        'load_terms' => 0,
                        'return_format' => 'object',
                        'field_type' => 'select',
                        'allow_null' => 0,
                        'allow_in_bindings' => 1,
                        'bidirectional' => 0,
                        'multiple' => 0,
                        'bidirectional_target' => [],
                    ],
                    [
                        'key' => 'field_category_items_count',
                        'label' => $titleCount,
                        'name' => 'category_items_count',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 4,
                        'min' => 1,
                        'max' => 12,
                        'allow_in_bindings' => 1,
                        'step' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_category_items_num',
                        'label' => $titleNum,
                        'name' => 'category_items_num',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 4,
                        'min' => 1,
                        'max' => 6,
                        'allow_in_bindings' => 1,
                        'step' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_category_button_title',
                        'label' => $titleButton,
                        'name' => 'category_button_title',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => $defaultButton,
                        'maxlength' => '',
                        'allow_in_bindings' => 0,
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_category_title_align',
                        'label' => $titleAlignment,
                        'name' => 'category_title_align',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'left' => $textAlignmentLeft,
                            'center' => $textAlignmentCenter,
                            'right' => $textAlignmentRight,
                        ],
                        'default_value' => 'left',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_category_texte_align',
                        'label' => $titleTextAlignment,
                        'name' => 'category_text_align',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'left' => $textAlignmentLeft,
                            'center' => $textAlignmentCenter,
                            'right' => $textAlignmentRight,
                        ],
                        'default_value' => 'left',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/category',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});