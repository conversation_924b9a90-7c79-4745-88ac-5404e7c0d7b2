<?php
/**
 * ACF Content Analysis for Yoast SEO test file.
 *
 * @package YoastACFAnalysis
 *
 * {@internal This code comes straight out of the ACF Export function.}}
 */

if( function_exists('acf_add_local_field_group') ):

	acf_add_local_field_group(array (
		'key' => 'group_592573d9be0dd',
		'title' => 'Yoast ACF Analysis Test Fields - ACF 5',
		'fields' => array (
			array (
				'key' => 'field_591eb45f2be86',
				'label' => 'Text',
				'name' => 'yoast_acf_analysis_text',
				'type' => 'text',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'placeholder' => '',
				'prepend' => '',
				'append' => '',
				'formatting' => 'html',
				'maxlength' => '',
			),
			array (
				'key' => 'field_591eb47e2be87',
				'label' => 'Text Area',
				'name' => 'yoast_acf_analysis_textarea',
				'type' => 'textarea',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'placeholder' => '',
				'maxlength' => '',
				'rows' => '',
				'new_lines' => 'br',
			),
			array (
				'key' => 'field_591eb4a92be89',
				'label' => 'Email',
				'name' => 'yoast_acf_analysis_email',
				'type' => 'email',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'placeholder' => '',
				'prepend' => '',
				'append' => '',
			),
			array (
				'key' => 'field_59256f74f2224',
				'label' => 'Url',
				'name' => 'yoast_acf_analysis_url',
				'type' => 'url',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'placeholder' => '',
			),
			array (
				'key' => 'field_59f14c1ce079d',
				'label' => 'Link',
				'name' => 'yoast_acf_analysis_link',
				'type' => 'link',
				'value' => NULL,
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'return_format' => 'array',
			),
			array (
				'key' => 'field_591eb4da2be8b',
				'label' => 'Wysiwyg',
				'name' => 'yoast_acf_analysis_wysiwyg',
				'type' => 'wysiwyg',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'default_value' => '',
				'toolbar' => 'full',
				'media_upload' => 1,
				'tabs' => 'all',
				'delay' => 0,
			),
			array (
				'key' => 'field_591eb4fa2be8c',
				'label' => 'Image',
				'name' => 'yoast_acf_analysis_image',
				'type' => 'image',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'preview_size' => 'thumbnail',
				'library' => 'all',
				'return_format' => 'array',
				'min_width' => 0,
				'min_height' => 0,
				'min_size' => 0,
				'max_width' => 0,
				'max_height' => 0,
				'max_size' => 0,
				'mime_types' => '',
			),
			array (
				'key' => 'field_59256f82f2225',
				'label' => 'Gallery',
				'name' => 'yoast_acf_analysis_gallery',
				'type' => 'gallery',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'min' => '',
				'max' => '',
				'insert' => 'append',
				'library' => 'all',
				'min_width' => '',
				'min_height' => '',
				'min_size' => '',
				'max_width' => '',
				'max_height' => '',
				'max_size' => '',
				'mime_types' => '',
			),
			array (
				'key' => 'field_591eb5152be8d',
				'label' => 'Taxonomy Checkbox',
				'name' => 'yoast_acf_analysis_taxonomy_checkbox',
				'type' => 'taxonomy',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'taxonomy' => 'category',
				'field_type' => 'checkbox',
				'allow_null' => 0,
				'load_save_terms' => 0,
				'return_format' => 'id',
				'multiple' => 0,
				'add_term' => 1,
				'load_terms' => 0,
				'save_terms' => 0,
			),
			array (
				'key' => 'field_591eb55c2be8e',
				'label' => 'Taxonomy Multi Select',
				'name' => 'yoast_acf_analysis_taxonomy_multiselect',
				'type' => 'taxonomy',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'taxonomy' => 'category',
				'field_type' => 'multi_select',
				'allow_null' => 0,
				'load_save_terms' => 0,
				'return_format' => 'id',
				'multiple' => 0,
				'add_term' => 1,
				'load_terms' => 0,
				'save_terms' => 0,
			),
			array (
				'key' => 'field_599d78aceff96',
				'label' => 'Flexible Content',
				'name' => 'yoast_acf_analysis_flexible_content',
				'type' => 'flexible_content',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'layouts' => array (
					'599d78b071bb6' => array (
						'key' => '599d78b071bb6',
						'name' => 'yoast_acf_analysis_flexible_content_layout',
						'label' => 'Text Layout',
						'display' => 'block',
						'sub_fields' => array (
							array (
								'key' => 'field_599ea47054535',
								'label' => 'Text (inside Flexible Content)',
								'name' => 'yoast_acf_analysis_flexible_content_text',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array (
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
								'maxlength' => '',
							),
						),
						'min' => '',
						'max' => '',
					),
				),
				'button_label' => 'Add Row',
				'min' => '',
				'max' => '',
			),
			array (
				'key' => 'field_599ea4a054536',
				'label' => 'Repeater',
				'name' => 'yoast_acf_analysis_repeater',
				'type' => 'repeater',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array (
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'collapsed' => '',
				'min' => 0,
				'max' => 0,
				'layout' => 'table',
				'button_label' => '',
				'sub_fields' => array (
					array (
						'key' => 'field_599ea4b054538',
						'label' => 'Text (inside Repeater)',
						'name' => 'yoast_acf_analysis_repeater_text',
						'type' => 'text',
						'instructions' => '',
						'required' => 0,
						'conditional_logic' => 0,
						'wrapper' => array (
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'maxlength' => '',
					),
				),
			),
			array(
				'key'               => 'field_59c25bf746213',
				'label'             => 'Group',
				'name'              => 'yoast_acf_analysis_group',
				'type'              => 'group',
				'instructions'      => '',
				'required'          => 0,
				'conditional_logic' => 0,
				'wrapper'           => array(
					'width' => '',
					'class' => '',
					'id'    => '',
				),
				'layout'            => 'block',
				'sub_fields'        => array(
					array(
						'key'               => 'field_59c25c5346214',
						'label'             => 'Text (inside Group)',
						'name'              => 'yoast_acf_analysis_group_text',
						'type'              => 'text',
						'instructions'      => '',
						'required'          => 0,
						'conditional_logic' => 0,
						'wrapper'           => array(
							'width' => '',
							'class' => '',
							'id'    => '',
						),
						'default_value'     => '',
						'placeholder'       => '',
						'prepend'           => '',
						'append'            => '',
						'maxlength'         => '',
					),
				),
			),
		),
		'location' => array (
			array (
				array (
					'param' => 'post_type',
					'operator' => '==',
					'value' => 'post',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'seamless',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => 1,
		'description' => '',
	));

endif;
