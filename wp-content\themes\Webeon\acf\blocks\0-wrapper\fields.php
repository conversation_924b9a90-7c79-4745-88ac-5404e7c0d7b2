<?php
add_action('acf/include_fields', function () {

    
    $title = 'Wrapper Settings';
    $titleImg = 'Image';
    $titleImageOpacity = 'Image Opacity';
    $titleShadow = 'Wrapper with shadow';
    $titleMargin = 'Enable margins';
    $titleTypeX = 'Image Type X';
    $titleTypeY = 'Image Type Y';
    $titleTypeFill = 'Fill';
    $titleTypeCenter = 'Center';
    $titleTypeRight = 'Right';
    $titleTypeLeft = 'Left';
    $titleTypeBottom = 'bottom';
    $titleTypeTop = 'top';
    $titleMp4 = 'Mp4 video';
    $titleWebm = 'Webm video';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Настройки обертки';
        $titleImg = 'Картинка';
        $titleTypeX = 'Тип отображения по горизонтали';
        $titleTypeY = 'Тип отображения по вертикали';
        $titleTypeFill = 'Заполнение';
        $titleTypeCenter = 'По центру';
        $titleTypeRight = 'Справа';
        $titleTypeLeft = 'Слева';
        $titleTypeBottom = 'Cнизу';
        $titleTypeTop = 'Cверху';
        $titleImageOpacity = 'Непрозрачность Картинки';
        $titleShadow = 'Обертка с тенью';
        $titleMargin = 'Включить внешние отступы';
        $titleMp4 = 'Mp4 видео';
        $titleWebm = 'Webm видео';
    }

    acf_add_local_field_group([
        'key' => 'wrapper',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_wrapper_group',
                'label' => $title,
                'name' => 'wrapper_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_wrapper_mp4',
                        'label' => $titleMp4,
                        'name' => 'wrapper_mp4',
                        'aria-label' => '',
                        'type' => 'file',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'url',
                        'library' => 'all',
                        'min_size' => '',
                        'max_size' => '',
                        'mime_types' => 'mp4',
                        'allow_in_bindings' => 0,
                    ],
                    [
                        'key' => 'field_wrapper_webm',
                        'label' => $titleWebm,
                        'name' => 'wrapper_webm',
                        'aria-label' => '',
                        'type' => 'file',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'url',
                        'library' => 'all',
                        'min_size' => '',
                        'max_size' => '',
                        'mime_types' => 'webm',
                        'allow_in_bindings' => 0,
                    ],
                    [
                        'key' => 'field_wrapper_img',
                        'label' => $titleImg,
                        'name' => 'wrapper_img',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'thumbnail',
                    ],
                    [
                        'key' => 'field_wrapper_img_type_x',
                        'label' => $titleTypeX,
                        'name' => 'wrapper_img_type_x',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '100%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'fill' => $titleTypeFill,
                            'center' => $titleTypeCenter,
                            'right' => $titleTypeRight,
                            'left' => $titleTypeLeft,
                        ],
                        'default_value' => 'fill',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_wrapper_img_type_y',
                        'label' => $titleTypeY,
                        'name' => 'wrapper_img_type_y',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => [
                            [
                                [
                                    'field' => 'field_wrapper_img_type_x',
                                    'operator' => '!=',
                                    'value' => 'fill',
                                ],
                            ],
                        ],
                        'wrapper' => [
                            'width' => '100%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'center' => $titleTypeCenter,
                            'top' => $titleTypeTop,
                            'bottom' => $titleTypeBottom,
                        ],
                        'default_value' => 'center',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_wrapper_image_opacity',
                        'label' => $titleImageOpacity,
                        'name' => 'wrapper_image_opacity',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 100,
                        'min' => 1,
                        'max' => 100,
                        'allow_in_bindings' => 1,
                        'step' => '0.1',
                        'prepend' => '',
                        'append' => '%',
                    ],
                    [
                        'key' => 'field_wrapper_shadow',
                        'label' => $titleShadow,
                        'name' => 'wrapper_shadow',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                    [
                        'key' => 'field_wrapper_margin',
                        'label' => $titleMargin,
                        'name' => 'wrapper_margin',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 1,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/wrapper',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});