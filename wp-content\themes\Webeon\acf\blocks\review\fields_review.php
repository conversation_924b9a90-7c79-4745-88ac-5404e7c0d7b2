<?php

add_action('acf/include_fields', function () {

    
    $title = 'Review';
    $titleAuthorName = 'Author Name';
    $titaleAuthorAdditionalInfo = 'Author Additional Info';
    $titleAuthorPhoto = 'Author Photo';
    $titleRaiting = 'Rating';
    $titleText = 'Text';
    $textAlignment = 'Review Content Alignment';
    $textAlignmentLeft = 'Left';
    $textAlignmentCenter = 'Center';
    $textAlignmentRight = 'Right';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Отзыв';
        $titleAuthorName = 'Имя автора';
        $titaleAuthorAdditionalInfo = 'Дополнительная информация об авторе';
        $titleAuthorPhoto = 'Фото автора';
        $titleRaiting = 'Рейтинг';
        $titleText = 'Отзыв';
        $textAlignment = 'Выравнивание контента отзыва';
        $textAlignmentLeft = 'Слева';
        $textAlignmentCenter = 'По центру';
        $textAlignmentRight = 'Справа';
    }

    acf_add_local_field_group([
        'key' => 'review',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_review_group',
                'label' => $title,
                'name' => 'review_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_review_author_name',
                        'label' => $titleAuthorName,
                        'name' => 'review_author_name',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'allow_in_bindings' => 0,
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_review_author_additional_info',
                        'label' => $titaleAuthorAdditionalInfo,
                        'name' => 'review_author_additional_info',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'allow_in_bindings' => 0,
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_review_author_photo',
                        'label' => $titleAuthorPhoto,
                        'name' => 'review_author_photo',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'thumbnail',
                    ],
                    [
                        'key' => 'field_review_rating',
                        'label' => $titleRaiting,
                        'name' => 'review_rating',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 5,
                        'min' => 1,
                        'max' => 5,
                        'allow_in_bindings' => 1,
                        'step' => 0.5,
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_review_align',
                        'label' => $textAlignment,
                        'name' => 'review_align',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'left' => $textAlignmentLeft,
                            'center' => $textAlignmentCenter,
                            'right' => $textAlignmentRight,
                        ],
                        'default_value' => 'center',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_review_text',
                        'label' => $titleText,
                        'name' => 'review_text',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'rows' => 3,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/review',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});