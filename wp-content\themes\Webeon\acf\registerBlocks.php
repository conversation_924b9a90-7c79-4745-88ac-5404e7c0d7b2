<?php

function registerBlocks() {

    $dir = __DIR__ . '/blocks/';
    $folders = array_diff(scandir($dir), array('..', '.'));
    foreach ($folders as $folder) {
        if (is_dir($dir . $folder)) {
            register_block_type($dir . $folder);
        }
    }
}

add_action( 'init', 'registerBlocks' );

$dir = __DIR__ . '/blocks/';
$folders = array_diff(scandir($dir), array('..', '.'));
foreach ($folders as $folder) {
    if (is_dir($dir . $folder)) {
        $filesList = scandir($dir . $folder);
        foreach ($filesList as $fileName) {
            if ($fileName !== '.' && $fileName !== '..' && $fileName == 'fields.php') {
                $filePath = $dir . $folder . '/' . $fileName;
                if (is_file($filePath)) {
                    include_once($filePath);
                }
            }
        }
    }
}

function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');