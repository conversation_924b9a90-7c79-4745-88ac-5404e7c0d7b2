<?php
add_action('acf/include_fields', function () {

    
    $title = 'Cards';
    $titleSettings = 'Settings';
    $titleBlockHeader = 'Block Title';
    $titleNum = 'Number of cards per row';
    $titleList = 'List';
    $titleAdd = 'Add Card';
    $titleHead = 'Title';
    $titleSub = 'Subtitle';
    $titleImgFullwidth = 'Image';
    $titleText = 'Text';
    $titleLink = 'Link';
    $textAlignment = 'Cards Text alignment';
    $textAlignmentLeft = 'Left';
    $textAlignmentCenter = 'Center';
    $textAlignmentJustify = 'Justify';
    $imageType = 'Image type';
    $imageTypeSmall = 'Icon (icon frontend - 32х32px)';
    $imageTypeNum = 'Numeric';
    $imageTypeFullwidth = 'Image';
    $imagePosition = 'Image position';
    $imagePositionLeft = 'Left';
    $imagePositionCenter = 'Center';
    $imagePositionRight = 'Right';
    $titlePaddingTypeSmall = 'Small';
    $titlePaddingTypeSmallest = 'Smallest';
    $titleColorText = 'Header Text Color';
    $titleColorSub = 'Sub Header Color';
    $titleColorBrand = 'Brand';
    $titleColorBrandAlt = 'Brand Alt';
    $titleColorBlack = 'Black';
    $titleColorWhite = 'White';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Карточки';
        $titleSettings = 'Настройки';
        $titleBlockHeader = 'Заголовок блока';
        $titleNum = 'Количество карточек в строку';
        $titleList = 'Перечень';
        $titleAdd = 'Добавить карточку';
        $titleHead = 'Заголовок';
        $titleSub = 'Подзаголовок';
        $titleImgFullwidth = 'Картинка';
        $titleLink = 'Ссылка';
        $titleText = 'Текст';
        $textAlignment = 'Выравнивание текста карточек';
        $textAlignmentLeft = 'Слева';
        $textAlignmentCenter = 'По центру';
        $textAlignmentJustify = 'По ширине';
        $imageType = 'Тип изображения';
        $imageTypeSmall = 'Иконка (выводится 32х32px)';
        $imageTypeNum = 'Числовой';
        $imageTypeFullwidth = 'Картинка';
        $imagePosition = 'Позиция изображения';
        $imagePositionLeft = 'Слева';
        $imagePositionCenter = 'По центру';
        $imagePositionRight = 'Справа';
        $titlePaddingType = 'Тип отступа';
        $titlePaddingTypeDefault = 'Стандартный';
        $titlePaddingTypeBigger = 'Увеличенный';
        $titlePaddingTypeSmall = 'Уменьшенный';
        $titlePaddingTypeSmallest = 'Минимальный';
        $titleColorText = 'Цвет текста заголовка';
        $titleColorSub = 'Цвет текста описания';
        $titleColorBrand = 'Бренд';
        $titleColorBrandAlt = 'Бренд Дополнительный';
        $titleColorBlack = 'Черный';
        $titleColorWhite = 'Белый';
    }

    acf_add_local_field_group([
        'key' => 'cards',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_cards_group',
                'label' => $title,
                'name' => 'cards_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_cards_main_tab',
                        'label' => $titleSettings,
                        'name' => 'cards_main_tab',
                        'aria-label' => '',
                        'type' => 'tab',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'placement' => 'top',
                        'endpoint' => 0,
                        'selected' => 1,
                    ],
                    [
                        'key' => 'field_cards_title',
                        'label' => $titleBlockHeader,
                        'name' => 'cards_title',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '65%',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'allow_in_bindings' => 0,
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_cards_num',
                        'label' => $titleNum,
                        'name' => 'cards_num',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '35%',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 4,
                        'min' => 1,
                        'max' => 6,
                        'allow_in_bindings' => 1,
                        'step' => '',
                        'prepend' => '',
                        'append' => '',
                    ],
                    [
                        'key' => 'field_cards_text_align',
                        'label' => $textAlignment,
                        'name' => 'cards_text_align',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'left' => $textAlignmentLeft,
                            'center' => $textAlignmentCenter,
                            'justify' => $textAlignmentJustify,
                        ],
                        'default_value' => 'left',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_img_size',
                        'label' => $imageType,
                        'name' => 'cards_img_size',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'small' => $imageTypeSmall,
                            'num' => $imageTypeNum,
                            'fullwidth' => $imageTypeFullwidth,
                        ],
                        'default_value' => 'small',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_img_position',
                        'label' => $imagePosition,
                        'name' => 'cards_img_position',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'left' => $imagePositionLeft,
                            'center' => $imagePositionCenter,
                            'right' => $imagePositionRight,
                        ],
                        'default_value' => 'right',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_padding_type',
                        'label' => $titlePaddingType,
                        'name' => 'cards_padding_type',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => [
                            [
                                [
                                    'field' => 'field_hero_type',
                                    'operator' => '==',
                                    'value' => 'fill',
                                ],
                            ],
                        ],
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'bigger' => $titlePaddingTypeBigger,
                            'default' => $titlePaddingTypeDefault,
                            'small' => $titlePaddingTypeSmall,
                            'smallest' => $titlePaddingTypeSmallest,
                        ],
                        'default_value' => 'default',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_title_color',
                        'label' => $titleColorText,
                        'name' => 'cards_title_color',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [                            
                            'black' => $titleColorBlack,
                            'white' => $titleColorWhite,
                            'brand' => $titleColorBrand,
                            'brand_alt' => $titleColorBrandAlt,
                        ],
                        'default_value' => 'black',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_subtitle_color',
                        'label' => $titleColorSub,
                        'name' => 'cards_subtitle_color',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [                            
                            'black' => $titleColorBlack,
                            'white' => $titleColorWhite,
                            'brand' => $titleColorBrand,
                            'brand_alt' => $titleColorBrandAlt,
                        ],
                        'default_value' => 'black',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_cards_tab',
                        'label' => $title,
                        'name' => 'cards_tab',
                        'aria-label' => '',
                        'type' => 'tab',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'placement' => 'top',
                        'endpoint' => 0,
                        'selected' => 0,
                    ],
                    [
                        'key' => 'field_cards_repeater',
                        'label' => $titleList,
                        'name' => 'cards_repeater',
                        'aria-label' => '',
                        'type' => 'repeater',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'layout' => 'block',
                        'pagination' => 0,
                        'min' => 0,
                        'max' => 0,
                        'collapsed' => 'field_card_title',
                        'button_label' => $titleAdd,
                        'rows_per_page' => 20,
                        'sub_fields' => [
                            [
                                'key' => 'field_card_title',
                                'label' => $titleHead,
                                'name' => 'card_title',
                                'aria-label' => '',
                                'type' => 'text',
                                'instructions' => '',
                                'required' => 0,
                                'conditional_logic' => 0,
                                'wrapper' => [
                                    'width' => '40%',
                                    'class' => '',
                                    'id' => '',
                                ],
                                'default_value' => '',
                                'maxlength' => '',
                                'allow_in_bindings' => 0,
                                'placeholder' => '',
                                'prepend' => '',
                                'append' => '',
                                'parent_repeater' => 'field_cards_repeater'
                            ],
                            [
                                'key' => 'field_card_subtitle',
                                'label' => $titleSub,
                                'name' => 'card_subtitle',
                                'aria-label' => '',
                                'type' => 'text',
                                'instructions' => '',
                                'required' => 0,
                                'conditional_logic' => 0,
                                'wrapper' => [
                                    'width' => '40%',
                                    'class' => '',
                                    'id' => '',
                                ],
                                'default_value' => '',
                                'maxlength' => '',
                                'allow_in_bindings' => 0,
                                'placeholder' => '',
                                'prepend' => '',
                                'append' => '',
                                'parent_repeater' => 'field_cards_repeater'
                            ],
                            [
                                'key' => 'field_card_link',
                                'label' => $titleLink,
                                'name' => 'card_link',
                                'aria-label' => '',
                                'type' => 'link',
                                'instructions' => '',
                                'required' => 0,
                                'conditional_logic' => 0,
                                'wrapper' => [
                                    'width' => '20%',
                                    'class' => '',
                                    'id' => '',
                                ],
                                'return_format' => 'array',
                                'parent_repeater' => 'field_cards_repeater'
                            ], 
                            [
                                'key' => 'field_card_img_fullwidth',
                                'label' => $titleImgFullwidth,
                                'name' => 'card_img_fullwidth',
                                'aria-label' => '',
                                'type' => 'image',
                                'instructions' => '',
                                'required' => 0,
                                'conditional_logic' => [
                                    [
                                        [
                                            'field' => 'field_cards_img_size',
                                            'operator' => '!=',
                                            'value' => 'num',
                                        ],
                                    ],
                                ],
                                'wrapper' => [
                                    'width' => '30%',
                                    'class' => '',
                                    'id' => '',
                                ],
                                'return_format' => 'id',
                                'library' => 'all',
                                'min_width' => '',
                                'min_height' => '',
                                'min_size' => '',
                                'max_width' => '',
                                'max_height' => '',
                                'max_size' => '',
                                'mime_types' => 'jpg,jpeg,png,svg',
                                'allow_in_bindings' => 0,
                                'preview_size' => 'thumbnail',
                                'parent_repeater' => 'field_cards_repeater'
                            ],
                            [
                                'key' => 'field_card_text',
                                'label' => $titleText,
                                'name' => 'card_text',
                                'aria-label' => '',
                                'type' => 'textarea',
                                'instructions' => '',
                                'required' => 0,
                                'conditional_logic' => 0,
                                'wrapper' => [
                                    'width' => '70%',
                                    'class' => '',
                                    'id' => '',
                                ],
                                'default_value' => '',
                                'maxlength' => '',
                                'rows' => 3,
                                'placeholder' => '',
                                'new_lines' => 'br',
                                'parent_repeater' => 'field_cards_repeater'
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/cards',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});