.wrapper {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.wrapper p:first-child {
    margin-top: 0;
}

.wrapper p {
    line-height: normal;
    margin-bottom: 1rem;
}

.wrapper p:last-child {
    margin-bottom: 0;
}

@media (max-width: 1480px) {
    .wrapper {
        max-width: 1200px;
    }
}

@media (max-width: 1240px) {
    .wrapper {
        max-width: 1024px;
    }
}

@media (max-width: 1064px) {
    .wrapper {
        padding: 0 1rem;
    }
}

.wrapper-section {
    width: 100%;
    position: relative;
    background-blend-mode: lighten;
}

.wrapper-section-has-margin {
    margin: 2rem 0;
}

.wrapper-section-hasnt-margin {
    margin: 0;
}

.wrapper-section-has-padding {
    padding: 1rem 0;
}

.wrapper-section.wrapper-full-height {
    min-height: 100vh;
    margin-top: 0;
    padding: 4rem 0;
}

.wrapper-section.wrapper-full-height .wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.2rem;
}

.wrapper-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    user-select: none;
    /* Standard syntax */
    -webkit-user-select: none;
    /* Safari, Chrome, Opera */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE 10+ */
    -webkit-touch-callout: none;
    /* iOS Safari */
}

.wrapper-bg video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wrapper-img-fill img,
.wrapper-img-fill svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wrapper-img-center img,
.wrapper-img-center svg {
    position: absolute;
    width: auto;
    height: 100%;
    left: 50%;
    transform: translateX(-50%);
}

.wrapper-img-right img,
.wrapper-img-right svg {
    position: absolute;
    width: auto;
    height: 100%;
    right: 0;
}

.wrapper-img-left img,
.wrapper-img-left svg {
    position: absolute;
    width: auto;
    height: 100%;
    left: 0;
}

.wrapper-img-vertical-bottom img,
.wrapper-img-vertical-bottom svg {
    bottom: 0;
}

.wrapper-img-vertical-top img,
.wrapper-img-vertical-top svg {
    top: 0;
}

.wrapper-img-vertical-center img,
.wrapper-img-vertical-center svg {
    top: 50%;
    transform: translatey(-50%);
}

.wrapper-shadow {
    background: var(--wrapper-bg);
    color: var(--wrapper-text);
    border-radius: var(--border-radius);
    position: relative;
    box-shadow: var(--box-shadow);
    padding: 2rem;
}