<?php
add_action('acf/include_fields', function () {

    $title = 'Simple Hero Block';
    $titleHeader = 'Header (H1)';
    $titleHeaderLikeTitle = 'Text of continues header';
    $titleHeaderText = 'Text after header';
    $titlePaddingType = 'Padding type';
    $titlePaddingTypeDefault = 'Default';
    $titlePaddingTypeBigger = 'Bigger';
    $titleUnderline = 'Add underline to header';
    $titleMarginTop = 'Enable margin at the top';
    $titleMargin = 'Enable margin at the bottom';
    $descriptionScroller = 'Use [text1, text2, text3] to create a scroller';
    $titleLink = 'Link';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Простой Заглавный Блок';
        $titleHeader = 'Заголовок (H1)';
        $titleHeaderLikeTitle = 'Текст продолжение заголовка';
        $titleHeaderText = 'Текст после заголовка';
        $titlePaddingType = 'Тип отступа';
        $titlePaddingTypeDefault = 'Стандартный';
        $titlePaddingTypeBigger = 'Увеличенный';
        $titleUnderline = 'Добавить подчеркивание заголовка';
        $titleMarginTop = 'Включить отступ сверху';
        $titleMargin = 'Включить отступ снизу';
        $descriptionScroller = 'Используйте [текст1, текст2, текст3] для создания скроллера';
        $titleLink = 'Ссылка';
    }

    acf_add_local_field_group([
        'key' => 'hero-simple',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_hero_simple_group',
                'label' => $title,
                'name' => 'hero_simple_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_hero_simple_head',
                        'label' => $titleHeader,
                        'name' => 'hero_simple_head',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => $titleHeader,
                        'maxlength' => '',
                        'rows' => 2,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_simple_head_like_title',
                        'label' => $titleHeaderLikeTitle,
                        'name' => 'hero_simple_head_like_title',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => $descriptionScroller,
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'rows' => 3,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_simple_text',
                        'label' => $titleHeaderText,
                        'name' => 'hero_simple_text',
                        'aria-label' => '',
                        'type' => 'textarea',
                        'instructions' => $descriptionScroller,
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => '',
                        'maxlength' => '',
                        'rows' => 3,
                        'placeholder' => '',
                        'new_lines' => 'br',
                    ],
                    [
                        'key' => 'field_hero_simple_link',
                        'label' => $titleLink,
                        'name' => 'hero_simple_link',
                        'aria-label' => '',
                        'type' => 'link',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'array',
                    ],
                    [
                        'key' => 'field_hero_simple_padding_type',
                        'label' => $titlePaddingType,
                        'name' => 'hero_simple_padding_type',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => [
                            [
                                [
                                    'field' => 'field_hero_simple_type',
                                    'operator' => '==',
                                    'value' => 'fill',
                                ],
                            ],
                        ],
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'choices' => [
                            'default' => $titlePaddingTypeDefault,
                            'bigger' => $titlePaddingTypeBigger,
                        ],
                        'default_value' => 'default',
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'allow_in_bindings' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                    ],
                    [
                        'key' => 'field_hero_simple_underline',
                        'label' => $titleUnderline,
                        'name' => 'hero_simple_underline',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 1,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                    [
                        'key' => 'field_hero_simple_margin_top',
                        'label' => $titleMarginTop,
                        'name' => 'hero_simple_margin_top',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                    [
                        'key' => 'field_hero_simple_margin',
                        'label' => $titleMargin,
                        'name' => 'hero_simple_margin',
                        'aria-label' => '',
                        'type' => 'true_false',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'message' => '',
                        'default_value' => 0,
                        'ui' => 1,
                        'ui_on_text' => '',
                        'ui_off_text' => '',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/hero-simple',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});