<?php

add_action('acf/include_fields', function () {

    
    $title = 'Tabs';
    $message = 'Block for displaying content in tabs';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'Вкладки';
        $message = 'Блок для от ображения контента в виде вкладок';
    }

    acf_add_local_field_group([
        'key' => 'tabs',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_tabs_message',
                'label' => $title,
                'name' => 'tabs_message',
                'aria-label' => '',
                'type' => 'message',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'message' => $message,
                'new_lines' => 'br',
                'esc_html' => 0,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/tabs',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});