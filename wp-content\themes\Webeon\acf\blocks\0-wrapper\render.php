<?php 

$blockName = basename(__DIR__);
$blockCss = str_replace('0-', '', $blockName) . '-block-style';

$listOfStyles = [
    $blockCss => [
        THEME_URL . '/acf/blocks/'. $blockName . '/style.css',
        __DIR__ . '/style.css',
    ],
];

REGISTERBLOCKASSETS::register($listOfStyles);

$blockSettings = $block['data'] ?? null;
$img = $blockSettings['wrapper_group_wrapper_img'] ?? null;
$imageOpacity = $blockSettings['wrapper_group_wrapper_image_opacity'] ?? 100;
$imageOpacity = $imageOpacity / 100;

$blockStyle = $block['style'] ?? null;
$blockStyleBackground = $blockStyle['color']['background'] ?? null;
$blockStyleGradient = $blockStyle['color']['gradient'] ?? null;

$anchor = null;

if(isset($block['anchor']) && is_string($block['anchor'])){
    $anchor = $block['anchor'];
}

$sectionStyle = 'style="';

if($blockStyleGradient){
    $sectionStyle .= 'background:'.$blockStyleGradient.';';
}else{
    if($blockStyleBackground){
        $sectionStyle .= 'background-color:'.$blockStyleBackground.';';
    }
}

$sectionStyle .= '"';

$wrapperShadow = $blockSettings['wrapper_shadow'] ?? false;

$wrapperFullHeight = $blockSettings['wrapper_height'] ?? false;

$wrapperHasMargin = $blockSettings['wrapper_margin'] ?? true;

$wrapperMarginClass = match ($wrapperHasMargin) {
    true => 'wrapper-section-has-margin',
    false => 'wrapper-section-hasnt-margin',
};

$wrapperHasPadding = $blockSettings['wrapper_padding'] ?? true;

$wrapperPaddingClass = match ($wrapperHasPadding) {
    true => 'wrapper-section-has-padding',
    false => 'wrapper-section-hasnt-padding',
};

$wrapperImgType = $blockSettings['wrapper_img_type'] ?? 'fill';

$wrapperImgTypeClass = match ($wrapperImgType) {
    'fill' => 'wrapper-img-fill',
    'center' => 'wrapper-img-center',
    'right' => 'wrapper-img-right',
    'left' => 'wrapper-img-left',
};
?> <pre><?php
var_dump($block); ?>
</pre>
<?php
// if( !empty( $block['styles'] ) ){
//     $classes = array_merge( $classes, explode( ' ', $block['className'] ) );
//     var_dump($classes);
// }
?>

<div class="wrapper-section <?= $wrapperMarginClass; ?> <?= $wrapperPaddingClass; ?> <?php if($img){ echo ' wrapper-section-bg'; } if($wrapperFullHeight){ echo ' wrapper-full-height'; } ?>" <?= $sectionStyle ?> <?php if($anchor){ ?> id="<?= $anchor ?>" <?php } ?>>
    <?php if($img && !$video){ ?>
        <div class="wrapper-bg <?= $wrapperImgTypeClass ?>" style="opacity: <?= $imageOpacity ?>;">
            <?= GOODIES::setImg($img); ?>
        </div>
    <?php } ?>
    <?php if($video){ ?>
        <div class="wrapper-bg <?= $wrapperImgTypeClass ?>" style="opacity: <?= $imageOpacity ?>;">
        <?php
        $poster = '';
        if($img){ $poster = 'poster="' . wp_get_attachment_image_url($img) . '"'; } ?>
        <video width="440px" loop="true" autoplay="autoplay" muted="" playsinline webkit-playsinline preload="metadata" <?= $poster ?> oncontextmenu="return false;">
            <?php if($video['mp4']){ ?><source src="<?= $video['mp4']; ?>" type="video/mp4" /><?php } ?>
            <?php if($video['webm']){ ?><source src="<?= $video['webm']; ?>" type="video/webm" /><?php } ?>
        </video>
        </div>
    <?php } ?>
    <div class="wrapper">
        <?php if($wrapperShadow){ echo '<div class="wrapper-shadow">'; } ?>
            <InnerBlocks />
        <?php if($wrapperShadow){ echo '</div>'; } ?>
    </div>
</div>