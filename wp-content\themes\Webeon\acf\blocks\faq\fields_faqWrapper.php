<?php

add_action('acf/include_fields', function () {

    
    $title = 'FAQ Wrapper';
    $message = 'Add a wrapper for creating a micro-schema FAQPage. It is not necessary to wrap Accedeon in this block, but if this is a semantic block then it is necessary to display it correctly in Google.';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'FAQ обертка';
        $message = 'Добавляет обёртку для создания микроразметки FAQPage. Необязательно оборачивать Accedeon в этот блок, но если это смысловой блок то необходимо для правильного отображения в Google.';
    }

    acf_add_local_field_group([
        'key' => 'faq_wrapper',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_faq_wrapper_message',
                'label' => $title,
                'name' => 'faq_wrapper_message',
                'aria-label' => '',
                'type' => 'message',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'message' => $message,
                'new_lines' => 'br',
                'esc_html' => 0,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/faq-wrapper',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});