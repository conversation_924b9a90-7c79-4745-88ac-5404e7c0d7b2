(()=>{var e={20:(e,t,r)=>{"use strict";var n=r(540),o=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),s=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var n,c={},l=null,u=null;for(n in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,n)&&!a.hasOwnProperty(n)&&(c[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===c[n]&&(c[n]=t[n]);return{$$typeof:o,type:e,key:l,ref:u,props:c,_owner:s.current}}t.jsx=c,t.jsxs=c},138:()=>{jQuery,acf.jsxNameReplacements={"accent-height":"accentHeight",accentheight:"accentHeight","accept-charset":"acceptCharset",acceptcharset:"acceptCharset",accesskey:"accessKey","alignment-baseline":"alignmentBaseline",alignmentbaseline:"alignmentBaseline",allowedblocks:"allowedBlocks",allowfullscreen:"allowFullScreen",allowreorder:"allowReorder","arabic-form":"arabicForm",arabicform:"arabicForm",attributename:"attributeName",attributetype:"attributeType",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autoreverse:"autoReverse",autosave:"autoSave",basefrequency:"baseFrequency","baseline-shift":"baselineShift",baselineshift:"baselineShift",baseprofile:"baseProfile",calcmode:"calcMode","cap-height":"capHeight",capheight:"capHeight",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className","clip-path":"clipPath","clip-rule":"clipRule",clippath:"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","color-interpolation":"colorInterpolation","color-interpolation-filters":"colorInterpolationFilters","color-profile":"colorProfile","color-rendering":"colorRendering",colorinterpolation:"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters",colorprofile:"colorProfile",colorrendering:"colorRendering",colspan:"colSpan",contenteditable:"contentEditable",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",diffuseconstant:"diffuseConstant",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback","dominant-baseline":"dominantBaseline",dominantbaseline:"dominantBaseline",edgemode:"edgeMode","enable-background":"enableBackground",enablebackground:"enableBackground",enctype:"encType",enterkeyhint:"enterKeyHint",externalresourcesrequired:"externalResourcesRequired","fill-opacity":"fillOpacity","fill-rule":"fillRule",fillopacity:"fillOpacity",fillrule:"fillRule",filterres:"filterRes",filterunits:"filterUnits","flood-color":"floodColor","flood-opacity":"floodOpacity",floodcolor:"floodColor",floodopacity:"floodOpacity","font-family":"fontFamily","font-size":"fontSize","font-size-adjust":"fontSizeAdjust","font-stretch":"fontStretch","font-style":"fontStyle","font-variant":"fontVariant","font-weight":"fontWeight",fontfamily:"fontFamily",fontsize:"fontSize",fontsizeadjust:"fontSizeAdjust",fontstretch:"fontStretch",fontstyle:"fontStyle",fontvariant:"fontVariant",fontweight:"fontWeight",for:"htmlFor",foreignobject:"foreignObject",formaction:"formAction",formenctype:"formEncType",formmethod:"formMethod",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder","glyph-name":"glyphName","glyph-orientation-horizontal":"glyphOrientationHorizontal","glyph-orientation-vertical":"glyphOrientationVertical",glyphname:"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits","horiz-adv-x":"horizAdvX","horiz-origin-x":"horizOriginX",horizadvx:"horizAdvX",horizoriginx:"horizOriginX",hreflang:"hrefLang",htmlfor:"htmlFor","http-equiv":"httpEquiv",httpequiv:"httpEquiv","image-rendering":"imageRendering",imagerendering:"imageRendering",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keyparams:"keyParams",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",keytype:"keyType",lengthadjust:"lengthAdjust","letter-spacing":"letterSpacing",letterspacing:"letterSpacing","lighting-color":"lightingColor",lightingcolor:"lightingColor",limitingconeangle:"limitingConeAngle",marginheight:"marginHeight",marginwidth:"marginWidth","marker-end":"markerEnd","marker-mid":"markerMid","marker-start":"markerStart",markerend:"markerEnd",markerheight:"markerHeight",markermid:"markerMid",markerstart:"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",numoctaves:"numOctaves","overline-position":"overlinePosition","overline-thickness":"overlineThickness",overlineposition:"overlinePosition",overlinethickness:"overlineThickness","paint-order":"paintOrder",paintorder:"paintOrder","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",playsinline:"playsInline","pointer-events":"pointerEvents",pointerevents:"pointerEvents",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",refx:"refX",refy:"refY","rendering-intent":"renderingIntent",renderingintent:"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",rowspan:"rowSpan","shape-rendering":"shapeRendering",shaperendering:"shapeRendering",specularconstant:"specularConstant",specularexponent:"specularExponent",spellcheck:"spellCheck",spreadmethod:"spreadMethod",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles","stop-color":"stopColor","stop-opacity":"stopOpacity",stopcolor:"stopColor",stopopacity:"stopOpacity","strikethrough-position":"strikethroughPosition","strikethrough-thickness":"strikethroughThickness",strikethroughposition:"strikethroughPosition",strikethroughthickness:"strikethroughThickness","stroke-dasharray":"strokeDasharray","stroke-dashoffset":"strokeDashoffset","stroke-linecap":"strokeLinecap","stroke-linejoin":"strokeLinejoin","stroke-miterlimit":"strokeMiterlimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth",strokedasharray:"strokeDasharray",strokedashoffset:"strokeDashoffset",strokelinecap:"strokeLinecap",strokelinejoin:"strokeLinejoin",strokemiterlimit:"strokeMiterlimit",strokeopacity:"strokeOpacity",strokewidth:"strokeWidth",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tabindex:"tabIndex",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",templatelock:"templateLock","text-anchor":"textAnchor","text-decoration":"textDecoration","text-rendering":"textRendering",textanchor:"textAnchor",textdecoration:"textDecoration",textlength:"textLength",textrendering:"textRendering","underline-position":"underlinePosition","underline-thickness":"underlineThickness",underlineposition:"underlinePosition",underlinethickness:"underlineThickness","unicode-bidi":"unicodeBidi","unicode-range":"unicodeRange",unicodebidi:"unicodeBidi",unicoderange:"unicodeRange","units-per-em":"unitsPerEm",unitsperem:"unitsPerEm",usemap:"useMap","v-alphabetic":"vAlphabetic","v-hanging":"vHanging","v-ideographic":"vIdeographic","v-mathematical":"vMathematical",valphabetic:"vAlphabetic","vector-effect":"vectorEffect",vectoreffect:"vectorEffect","vert-adv-y":"vertAdvY","vert-origin-x":"vertOriginX","vert-origin-y":"vertOriginY",vertadvy:"vertAdvY",vertoriginx:"vertOriginX",vertoriginy:"vertOriginY",vhanging:"vHanging",videographic:"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",vmathematical:"vMathematical","word-spacing":"wordSpacing",wordspacing:"wordSpacing","writing-mode":"writingMode",writingmode:"writingMode","x-height":"xHeight",xchannelselector:"xChannelSelector",xheight:"xHeight","xlink:actuate":"xlinkActuate","xlink:arcrole":"xlinkArcrole","xlink:href":"xlinkHref","xlink:role":"xlinkRole","xlink:show":"xlinkShow","xlink:title":"xlinkTitle","xlink:type":"xlinkType",xlinkactuate:"xlinkActuate",xlinkarcrole:"xlinkArcrole",xlinkhref:"xlinkHref",xlinkrole:"xlinkRole",xlinkshow:"xlinkShow",xlinktitle:"xlinkTitle",xlinktype:"xlinkType","xml:base":"xmlBase","xml:lang":"xmlLang","xml:space":"xmlSpace",xmlbase:"xmlBase",xmllang:"xmlLang","xmlns:xlink":"xmlnsXlink",xmlnsxlink:"xmlnsXlink",xmlspace:"xmlSpace",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"}},151:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};e.exports=t},206:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},287:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),f=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function b(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||h}function y(){}function k(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||h}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var v=k.prototype=new y;v.constructor=k,m(v,b.prototype),v.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,_={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,n){var o,i={},s=null,a=null;if(null!=t)for(o in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(s=""+t.key),t)w.call(t,o)&&!S.hasOwnProperty(o)&&(i[o]=t[o]);var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){for(var l=Array(c),u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(o in c=e.defaultProps)void 0===i[o]&&(i[o]=c[o]);return{$$typeof:r,type:e,key:s,ref:a,props:i,_owner:_.current}}function A(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var I=/\/+/g;function C(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(e,t,o,i,s){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var c=!1;if(null===e)c=!0;else switch(a){case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case r:case n:c=!0}}if(c)return s=s(c=e),e=""===i?"."+C(c,0):i,x(s)?(o="",null!=e&&(o=e.replace(I,"$&/")+"/"),T(s,t,o,"",function(e){return e})):null!=s&&(A(s)&&(s=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,o+(!s.key||c&&c.key===s.key?"":(""+s.key).replace(I,"$&/")+"/")+e)),t.push(s)),1;if(c=0,i=""===i?".":i+":",x(e))for(var l=0;l<e.length;l++){var u=i+C(a=e[l],l);c+=T(a,t,o,u,s)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),l=0;!(a=e.next()).done;)c+=T(a=a.value,t,o,u=i+C(a,l++),s);else if("object"===a)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return c}function B(e,t,r){if(null==e)return e;var n=[],o=0;return T(e,n,"","",function(e){return t.call(r,e,o++)}),n}function E(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},O={transition:null},P={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:O,ReactCurrentOwner:_};function $(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:B,forEach:function(e,t,r){B(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return B(e,function(){t++}),t},toArray:function(e){return B(e,function(e){return e})||[]},only:function(e){if(!A(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=o,t.Profiler=s,t.PureComponent=k,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=P,t.act=$,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),i=e.key,s=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,a=_.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(l in t)w.call(t,l)&&!S.hasOwnProperty(l)&&(o[l]=void 0===t[l]&&void 0!==c?c[l]:t[l])}var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){c=Array(l);for(var u=0;u<l;u++)c[u]=arguments[u+2];o.children=c}return{$$typeof:r,type:e.type,key:i,ref:s,props:o,_owner:a}},t.createContext=function(e){return(e={$$typeof:c,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:E}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},t.unstable_act=$,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,r){return R.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,r){return R.current.useReducer(e,t,r)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return R.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},503:(e,t,r)=>{var n,o,i,s,a;n=r(939),o=r(151).utf8,i=r(206),s=r(151).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?s.stringToBytes(e):o.stringToBytes(e):i(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=n.bytesToWords(e),c=8*e.length,l=1732584193,u=-271733879,p=-1732584194,d=271733878,f=0;f<r.length;f++)r[f]=16711935&(r[f]<<8|r[f]>>>24)|4278255360&(r[f]<<24|r[f]>>>8);r[c>>>5]|=128<<c%32,r[14+(c+64>>>9<<4)]=c;var h=a._ff,m=a._gg,g=a._hh,b=a._ii;for(f=0;f<r.length;f+=16){var y=l,k=u,v=p,x=d;l=h(l,u,p,d,r[f+0],7,-680876936),d=h(d,l,u,p,r[f+1],12,-389564586),p=h(p,d,l,u,r[f+2],17,606105819),u=h(u,p,d,l,r[f+3],22,-1044525330),l=h(l,u,p,d,r[f+4],7,-176418897),d=h(d,l,u,p,r[f+5],12,1200080426),p=h(p,d,l,u,r[f+6],17,-1473231341),u=h(u,p,d,l,r[f+7],22,-45705983),l=h(l,u,p,d,r[f+8],7,1770035416),d=h(d,l,u,p,r[f+9],12,-1958414417),p=h(p,d,l,u,r[f+10],17,-42063),u=h(u,p,d,l,r[f+11],22,-1990404162),l=h(l,u,p,d,r[f+12],7,1804603682),d=h(d,l,u,p,r[f+13],12,-40341101),p=h(p,d,l,u,r[f+14],17,-1502002290),l=m(l,u=h(u,p,d,l,r[f+15],22,1236535329),p,d,r[f+1],5,-165796510),d=m(d,l,u,p,r[f+6],9,-1069501632),p=m(p,d,l,u,r[f+11],14,643717713),u=m(u,p,d,l,r[f+0],20,-373897302),l=m(l,u,p,d,r[f+5],5,-701558691),d=m(d,l,u,p,r[f+10],9,38016083),p=m(p,d,l,u,r[f+15],14,-660478335),u=m(u,p,d,l,r[f+4],20,-405537848),l=m(l,u,p,d,r[f+9],5,568446438),d=m(d,l,u,p,r[f+14],9,-1019803690),p=m(p,d,l,u,r[f+3],14,-187363961),u=m(u,p,d,l,r[f+8],20,1163531501),l=m(l,u,p,d,r[f+13],5,-1444681467),d=m(d,l,u,p,r[f+2],9,-51403784),p=m(p,d,l,u,r[f+7],14,1735328473),l=g(l,u=m(u,p,d,l,r[f+12],20,-1926607734),p,d,r[f+5],4,-378558),d=g(d,l,u,p,r[f+8],11,-2022574463),p=g(p,d,l,u,r[f+11],16,1839030562),u=g(u,p,d,l,r[f+14],23,-35309556),l=g(l,u,p,d,r[f+1],4,-1530992060),d=g(d,l,u,p,r[f+4],11,1272893353),p=g(p,d,l,u,r[f+7],16,-155497632),u=g(u,p,d,l,r[f+10],23,-1094730640),l=g(l,u,p,d,r[f+13],4,681279174),d=g(d,l,u,p,r[f+0],11,-358537222),p=g(p,d,l,u,r[f+3],16,-722521979),u=g(u,p,d,l,r[f+6],23,76029189),l=g(l,u,p,d,r[f+9],4,-640364487),d=g(d,l,u,p,r[f+12],11,-421815835),p=g(p,d,l,u,r[f+15],16,530742520),l=b(l,u=g(u,p,d,l,r[f+2],23,-995338651),p,d,r[f+0],6,-198630844),d=b(d,l,u,p,r[f+7],10,1126891415),p=b(p,d,l,u,r[f+14],15,-1416354905),u=b(u,p,d,l,r[f+5],21,-57434055),l=b(l,u,p,d,r[f+12],6,1700485571),d=b(d,l,u,p,r[f+3],10,-1894986606),p=b(p,d,l,u,r[f+10],15,-1051523),u=b(u,p,d,l,r[f+1],21,-2054922799),l=b(l,u,p,d,r[f+8],6,1873313359),d=b(d,l,u,p,r[f+15],10,-30611744),p=b(p,d,l,u,r[f+6],15,-1560198380),u=b(u,p,d,l,r[f+13],21,1309151649),l=b(l,u,p,d,r[f+4],6,-145523070),d=b(d,l,u,p,r[f+11],10,-1120210379),p=b(p,d,l,u,r[f+2],15,718787259),u=b(u,p,d,l,r[f+9],21,-343485551),l=l+y>>>0,u=u+k>>>0,p=p+v>>>0,d=d+x>>>0}return n.endian([l,u,p,d])})._ff=function(e,t,r,n,o,i,s){var a=e+(t&r|~t&n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._gg=function(e,t,r,n,o,i,s){var a=e+(t&n|r&~n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._hh=function(e,t,r,n,o,i,s){var a=e+(t^r^n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._ii=function(e,t,r,n,o,i,s){var a=e+(r^(t|~n))+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=n.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?s.bytesToString(r):n.bytesToHex(r)}},540:(e,t,r)=>{"use strict";e.exports=r(287)},848:(e,t,r)=>{"use strict";e.exports=r(20)},939:e=>{var t,r;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&r.rotl(e,8)|4278255360&r.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=r.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,n=0;r<e.length;r++,n+=8)t[n>>>5]|=e[r]<<24-n%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var r=[],n=0;n<e.length;n+=3)for(var o=e[n]<<16|e[n+1]<<8|e[n+2],i=0;i<4;i++)8*n+6*i<=8*e.length?r.push(t.charAt(o>>>6*(3-i)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var r=[],n=0,o=0;n<e.length;o=++n%4)0!=o&&r.push((t.indexOf(e.charAt(n-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(n))>>>6-2*o);return r}},e.exports=r}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}(()=>{"use strict";r(138);var e=r(848);const t=r(503);((r,n)=>{const{BlockControls:o,InspectorControls:i,InnerBlocks:s,useBlockProps:a,AlignmentToolbar:c,BlockVerticalAlignmentToolbar:l}=wp.blockEditor,{ToolbarGroup:u,ToolbarButton:p,Placeholder:d,Spinner:f}=wp.components,{Fragment:h}=wp.element,{Component:m}=React,{useSelect:g}=wp.data,{createHigherOrderComponent:b}=wp.compose,y=wp.blockEditor.__experimentalBlockAlignmentMatrixToolbar||wp.blockEditor.BlockAlignmentMatrixToolbar,k=wp.blockEditor.__experimentalBlockAlignmentMatrixControl||wp.blockEditor.BlockAlignmentMatrixControl,v=wp.blockEditor.__experimentalBlockFullHeightAligmentControl||wp.blockEditor.__experimentalBlockFullHeightAlignmentControl||wp.blockEditor.BlockFullHeightAlignmentControl,x=wp.blockEditor.__experimentalUseInnerBlocksProps||wp.blockEditor.useInnerBlocksProps,w={};function _(e){return w[e]||!1}function S(e){return _(e).acf_block_version||1}function j(e){return _(e).validate}function A(e){const t=wp.data.select("core/block-editor").getBlockParents(e);return wp.data.select("core/block-editor").getBlocksByClientId(t).filter(e=>"core/query"===e.name).length}function I(){return document.querySelectorAll('iframe[name="editor-canvas"]').length>0}function C(t){const r=t.post_types||[];if(r.length){r.push("wp_block");const e=acf.get("postType");if(!r.includes(e))return!1}if("string"==typeof t.icon&&"<svg"===t.icon.substr(0,4)){const r=t.icon;t.icon=(0,e.jsx)(V,{children:r})}t.icon||delete t.icon,wp.blocks.getCategories().filter(({slug:e})=>e===t.category).pop()||(t.category="common"),t=acf.parseArgs(t,{title:"",name:"",category:"",api_version:2,acf_block_version:1});for(const e in t.attributes)"default"in t.attributes[e]&&0===t.attributes[e].default.length&&delete t.attributes[e].default;t.supports.anchor&&(t.attributes.anchor={type:"string"});let i=H,s=N;var a;(t.supports.alignText||t.supports.align_text)&&(t.attributes=Y(t.attributes,"align_text","string"),i=function(t,r){const n=X;return r.alignText=n(r.alignText),class extends m{render(){const{attributes:r,setAttributes:i}=this.props,{alignText:s}=r;return(0,e.jsxs)(h,{children:[(0,e.jsx)(o,{group:"block",children:(0,e.jsx)(c,{value:n(s),onChange:function(e){i({alignText:n(e)})}})}),(0,e.jsx)(t,{...this.props})]})}}}(i,t)),(t.supports.alignContent||t.supports.align_content)&&(t.attributes=Y(t.attributes,"align_content","string"),i=function(t,r){let i,s,a=r.supports.align_content||r.supports.alignContent;return"matrix"===a?(i=k||y,s=J):(i=l,s=W),i===n?(console.warn(`The "${a}" alignment component was not found.`),t):(r.alignContent=s(r.alignContent),class extends m{render(){const{attributes:r,setAttributes:n}=this.props,{alignContent:a}=r;return(0,e.jsxs)(h,{children:[(0,e.jsx)(o,{group:"block",children:(0,e.jsx)(i,{label:acf.__("Change content alignment"),value:s(a),onChange:function(e){n({alignContent:s(e)})}})}),(0,e.jsx)(t,{...this.props})]})}})}(i,t)),(t.supports.fullHeight||t.supports.full_height)&&(t.attributes=Y(t.attributes,"full_height","boolean"),a=i,t.blockType,i=v?class extends m{render(){const{attributes:t,setAttributes:r}=this.props,{fullHeight:n}=t;return(0,e.jsxs)(h,{children:[(0,e.jsx)(o,{group:"block",children:(0,e.jsx)(v,{isActive:n,onToggle:function(e){r({fullHeight:e})}})}),(0,e.jsx)(a,{...this.props})]})}}:a),t.edit=t=>(wp.element.useEffect(()=>()=>{wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").unlockPostSaving("acf/block/"+t.clientId)},[]),(0,e.jsx)(i,{...t})),t.save=()=>(0,e.jsx)(s,{}),w[t.name]=t;const u=wp.blocks.registerBlockType(t.name,t);return u.attributes.anchor&&(u.attributes.anchor={type:"string"}),u}acf.blockInstances={};const T={},B={};function E(e){const{attributes:n={},context:o={},query:i={},clientId:s=null,delay:a=0}=e,c=t(JSON.stringify({...n,...o,...i})),l=T[c]||{query:{},timeout:!1,promise:r.Deferred(),started:!1};return l.query={...l.query,...i},l.started||(clearTimeout(l.timeout),l.timeout=setTimeout(()=>{l.started=!0,B[c]?(T[c]=null,l.promise.resolve.apply(B[c][0],B[c][1])):r.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax({action:"acf/ajax/fetch-block",block:JSON.stringify(n),clientId:s,context:JSON.stringify(o),query:l.query})}).always(()=>{T[c]=null}).done(function(){B[c]=[this,arguments],l.promise.resolve.apply(this,arguments)}).fail(function(){l.promise.reject.apply(this,arguments)})},a),T[c]=l),l.promise}function R(e,t){return JSON.stringify(e)===JSON.stringify(t)}function O(t,r,n=0){const o=function(e,t){switch(e){case"innerblocks":return t<2?s:"ACFInnerBlocks";case"script":return L;case"#comment":return null;default:e=P(e)}return e}(t.nodeName.toLowerCase(),r);if(!o)return null;const i={};if(1===n&&"ACFInnerBlocks"!==o&&(i.ref=React.createRef()),acf.arrayArgs(t.attributes).map(F).forEach(({name:e,value:t})=>{i[e]=t}),"ACFInnerBlocks"===o)return(0,e.jsx)($,{...i});const a=[o,i];return acf.arrayArgs(t.childNodes).forEach(e=>{if(e instanceof Text){const t=e.textContent;t&&a.push(t)}else a.push(O(e,r,n+1))}),React.createElement.apply(this,a)}function P(e){return acf.isget(acf,"jsxNameReplacements",e)||e}function $(t){const{className:r="acf-innerblocks-container"}=t,n=x({className:r},t);return(0,e.jsx)("div",{...n,children:n.children})}function F(e){let t=e.name,r=e.value,n=acf.applyFilters("acf_blocks_parse_node_attr",!1,e);if(n)return n;switch(t){case"class":t="className";break;case"style":const e={};r.split(";").forEach(t=>{const r=t.indexOf(":");if(r>0){let n=t.substr(0,r).trim();const o=t.substr(r+1).trim();"-"!==n.charAt(0)&&(n=acf.strCamelCase(n)),e[n]=o}}),r=e;break;default:if(0===t.indexOf("data-"))break;t=P(t);const n=r.charAt(0);"["!==n&&"{"!==n||(r=JSON.parse(r)),"true"!==r&&"false"!==r||(r="true"===r)}return{name:t,value:r}}acf.parseJSX=(e,t)=>(e=(e="<div>"+e+"</div>").replace(/<InnerBlocks([^>]+)?\/>/,"<InnerBlocks$1></InnerBlocks>"),O(r(e)[0],t,0).props.children);const M=b(t=>class extends m{constructor(e){super(e);const{name:t,attributes:r}=this.props,o=_(t);if(!o)return;Object.keys(r).forEach(e=>{""===r[e]&&delete r[e]});const i={full_height:"fullHeight",align_content:"alignContent",align_text:"alignText"};Object.keys(i).forEach(e=>{r[e]!==n?r[i[e]]=r[e]:r[i[e]]===n&&o[e]!==n&&(r[i[e]]=o[e]),delete o[e],delete r[e]});for(let e in o.attributes)r[e]===n&&o[e]!==n&&(r[e]=o[e])}render(){return(0,e.jsx)(t,{...this.props})}},"withDefaultAttributes");function N(){return(0,e.jsx)(s.Content,{})}wp.hooks.addFilter("editor.BlockListBlock","acf/with-default-attributes",M);class H extends m{constructor(e){super(e),this.setup()}setup(){const{name:e,attributes:t,clientId:r}=this.props,n=_(e);function o(e){e.includes(t.mode)||(t.mode=e[0])}if(A(r)||I())o(["preview"]);else switch(n.mode){case"edit":o(["edit","preview"]);break;case"preview":o(["preview","edit"]);break;default:o(["auto"])}}render(){const{name:t,attributes:r,setAttributes:n,clientId:s}=this.props,a=_(t),c=A(s)||I();let{mode:l}=r;c&&(l="preview");let d=a.supports.mode;("auto"===l||c)&&(d=!1);const f="preview"===l?acf.__("Switch to Edit"):acf.__("Switch to Preview"),m="preview"===l?"edit":"welcome-view-site";return(0,e.jsxs)(h,{children:[(0,e.jsx)(o,{children:d&&(0,e.jsx)(u,{children:(0,e.jsx)(p,{className:"components-icon-button components-toolbar__control",label:f,icon:m,onClick:function(){n({mode:"preview"===l?"edit":"preview"})}})})}),(0,e.jsx)(i,{children:"preview"===l&&(0,e.jsx)("div",{className:"acf-block-component acf-block-panel",children:(0,e.jsx)(z,{...this.props})})}),(0,e.jsx)(D,{...this.props})]})}}function D(t){const{attributes:r,isSelected:n,name:o,clientId:i}=t,{mode:s}=r,c=g(e=>{const t=e("core/block-editor").getBlockRootClientId(i);return e("core/block-editor").getBlockIndex(i,t)});let l=!0,u="acf-block-component acf-block-body";return("auto"===s&&!n||"preview"===s)&&(u+=" acf-block-preview",l=!1),i in acf.blockInstances||(acf.blockInstances[i]={validation_errors:!1,mode:s}),acf.blockInstances[i].mode=s,n||(j(o)&&acf.blockInstances[i].validation_errors&&(u+=" acf-block-has-validation-error"),acf.blockInstances[i].has_been_deselected=!0),S(o)>1?(0,e.jsx)("div",{...a({className:u}),children:l?(0,e.jsx)(z,{...t,index:c}):(0,e.jsx)(q,{...t,index:c})}):(0,e.jsx)("div",{...a(),children:(0,e.jsx)("div",{className:"acf-block-component acf-block-body",children:l?(0,e.jsx)(z,{...t,index:c}):(0,e.jsx)(q,{...t,index:c})})})}class V extends m{render(){return(0,e.jsx)("div",{dangerouslySetInnerHTML:{__html:this.props.children}})}}class L extends m{render(){return(0,e.jsx)("div",{ref:e=>this.el=e})}setHTML(e){r(this.el).html(`<script>${e}<\/script>`)}componentDidUpdate(){this.setHTML(this.props.children)}componentDidMount(){this.setHTML(this.props.children)}}class U extends m{constructor(e){super(e),this.setRef=this.setRef.bind(this),this.id="",this.el=!1,this.subscribed=!0,this.renderMethod="jQuery",this.passedValidation=!1,this.setup(e),this.loadState()}setup(e){const t=this.constructor.name,r=e.clientId;r in acf.blockInstances||(acf.blockInstances[r]={validation_errors:!1,mode:e.mode}),t in acf.blockInstances[r]||(acf.blockInstances[r][t]={})}fetch(){}maybePreload(e,t,r){if(acf.debug("Preload check",e,t,r),A(this.props.clientId))return acf.debug("Preload failed: Block is in query loop."),!1;const n=acf.get("preloadedBlocks");if(!n||!n[e])return acf.debug("Preload failed: Block not preloaded."),!1;const o={...n[e]};return r&&!o.form||!r&&o.form?(acf.debug("Preload failed: Correct state not preloaded.",r?"form":"preview"),!1):(o.html=o.html.replaceAll(e,t),o.validation&&o.validation.errors&&(o.validation.errors=o.validation.errors.map(r=>(r.input=r.input.replaceAll(e,t),r))),acf.debug("Preload successful",o),o)}loadState(){const e=acf.blockInstances[this.props.clientId]||{};this.state=e[this.constructor.name]||{}}setState(e){acf.blockInstances[this.props.clientId][this.constructor.name]={...this.state,...e},(this.subscribed||acf.get("StrictMode"))&&super.setState(e),acf.debug("SetState",Object.assign({},this),this.props.clientId,this.constructor.name,Object.assign({},acf.blockInstances[this.props.clientId][this.constructor.name]))}setHtml(e){if((e=e?e.trim():"")===this.state.html)return;const t={html:e};if("jsx"===this.renderMethod){if(t.jsx=acf.parseJSX(e,S(this.props.name)),t.jsx||(console.warn("Your ACF block template contains no valid HTML elements. Appending a empty div to prevent React JS errors."),t.html+="<div></div>",t.jsx=acf.parseJSX(t.html,S(this.props.name))),Array.isArray(t.jsx)){let e=t.jsx.find(e=>React.isValidElement(e));t.ref=e.ref}else t.ref=t.jsx.ref;t.$el=r(this.el)}else t.$el=r(e);this.setState(t)}setRef(e){this.el=e}render(){return this.state.jsx?S(this.props.name)>1?(this.setRef(this.state.jsx),this.state.jsx):(0,e.jsx)("div",{ref:this.setRef,children:this.state.jsx}):(0,e.jsx)("div",{ref:this.setRef,children:(0,e.jsx)(d,{children:(0,e.jsx)(f,{})})})}shouldComponentUpdate({index:e},{html:t}){return e!==this.props.index&&this.componentWillMove(),t!==this.state.html}display(e){if("jQuery"===this.renderMethod){const t=this.state.$el,n=t.parent(),o=r(this.el);acf.get("StrictMode")&&"append"!==e&&!this.subscribed||o.html(t),n.length&&n[0]!==o[0]&&n.html(t.clone())}switch(this.getValidationErrors()&&this.isNotNewlyAdded()?this.lockBlockForSaving():this.unlockBlockForSaving(),e){case"append":this.componentDidAppend();break;case"remount":this.componentDidRemount()}}validate(){}componentDidMount(){this.state.html===n?this.fetch():this.display("remount")}componentDidUpdate(e,t){this.display("append")}componentDidAppend(){acf.doAction("append",this.state.$el)}componentWillUnmount(){acf.get("StrictMode")&&!this.subscribed||acf.doAction("unmount",this.state.$el),this.subscribed=!1}componentDidRemount(){this.subscribed=!0,setTimeout(()=>{acf.doAction("remount",this.state.$el)})}componentWillMove(){acf.doAction("unmount",this.state.$el),setTimeout(()=>{acf.doAction("remount",this.state.$el)})}isNotNewlyAdded(){return acf.blockInstances[this.props.clientId].has_been_deselected||!1}hasShownValidation(){return acf.blockInstances[this.props.clientId].shown_validation||!1}setShownValidation(){acf.blockInstances[this.props.clientId].shown_validation=!0}setValidationErrors(e){acf.blockInstances[this.props.clientId].validation_errors=e}getValidationErrors(){return acf.blockInstances[this.props.clientId].validation_errors}getMode(){return acf.blockInstances[this.props.clientId].mode}lockBlockForSaving(){wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").lockPostSaving("acf/block/"+this.props.clientId)}unlockBlockForSaving(){wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").unlockPostSaving("acf/block/"+this.props.clientId)}displayValidation(e){if(!j(this.props.name))return void acf.debug("Block does not support validation");if(!e||e.hasClass("acf-empty-block-fields"))return void acf.debug("There is no edit form available to validate.");const t=this.getValidationErrors();acf.debug("Starting handle validation",Object.assign({},this),Object.assign({},e),t),this.setShownValidation();let r=acf.getBlockFormValidator(e);r.clearErrors(),acf.doAction("blocks/validation/pre_apply",t),t?(r.addErrors(t),r.showErrors("after"),this.lockBlockForSaving()):(r.has("notice")&&(r.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),r.set("notice",null)),this.unlockBlockForSaving()),acf.doAction("blocks/validation/post_apply",t)}}class z extends U{setup(e){this.id=`BlockForm-${e.clientId}`,super.setup(e)}fetch(e=!1,t=!1){const{context:r,clientId:n,name:o}=this.props;let{attributes:i}=this.props,s={form:!0};e&&(s={validate:!0},i.data=t);const a=Q(i,r);acf.debug("BlockForm fetch",i,s);const c=this.maybePreload(a,n,!0);if(c)return this.setHtml(acf.applyFilters("blocks/form/render",c.html,!0)),void(c.validation&&this.setValidationErrors(c.validation.errors));j(o)||(s.validate=!1),E({attributes:i,context:r,clientId:n,query:s}).done(({data:e})=>{acf.debug("fetch block form promise"),e?(e.form&&this.setHtml(acf.applyFilters("blocks/form/render",e.form.replaceAll(e.clientId,n),!1)),e.validation&&this.setValidationErrors(e.validation.errors),this.isNotNewlyAdded()&&(acf.debug("Block has already shown it's invalid. The form needs to show validation errors"),this.validate())):this.setHtml(`<div class="acf-block-fields acf-fields acf-empty-block-fields">${acf.__("Error loading block form")}</div>`)})}validate(e=!0){e&&this.loadState(),acf.debug("BlockForm calling validate with state",Object.assign({},this)),super.displayValidation(this.state.$el)}shouldComponentUpdate(e,t){return j(this.props.name)&&this.state.$el&&this.isNotNewlyAdded()&&!this.hasShownValidation()&&this.validate(!1),super.shouldComponentUpdate(e,t)}componentWillUnmount(){super.componentWillUnmount(),acf.debug("BlockForm Component did unmount")}componentDidRemount(){super.componentDidRemount(),acf.debug("BlockForm component did remount");const{$el:e}=this.state;j(this.props.name)&&this.isNotNewlyAdded()&&(acf.debug("Block has already shown it's invalid. The form needs to show validation errors"),this.validate()),!0!==e.data("acf-events-added")&&this.componentDidAppend()}componentDidAppend(){super.componentDidAppend(),acf.debug("BlockForm component did append");const{attributes:e,setAttributes:t,clientId:r,name:n}=this.props,o=this,{$el:i}=this.state;function s(s=!1){const a=acf.serialize(i,`acf-block_${r}`);s?e.data=a:t({data:a}),j(n)&&!s&&"edit"===o.getMode()&&(acf.debug("No block preview currently available. Need to trigger a validation only fetch."),o.fetch(!0,a))}let a=!1;i.on("change keyup",()=>{clearTimeout(a),a=setTimeout(s,300)}),i.data("acf-events-added",!0),e.data||s(!0)}}class q extends U{setup(e){const t=_(e.name),r=acf.isget(this.props,"context","postId");this.id=`BlockPreview-${e.clientId}`,super.setup(e),r&&(this.id=`BlockPreview-${e.clientId}-${r}`),t.supports.jsx&&(this.renderMethod="jsx")}fetch(e={}){const{attributes:t=this.props.attributes,clientId:r=this.props.clientId,context:n=this.props.context,delay:o=0}=e,{name:i}=this.props;this.setState({prevAttributes:t,prevContext:n});const s=Q(t,n);let a=this.maybePreload(s,r,!1);if(a)return 1==S(i)&&(a.html='<div class="acf-block-preview">'+a.html+"</div>"),this.setHtml(acf.applyFilters("blocks/preview/render",a.html,!0)),void(a.validation&&this.setValidationErrors(a.validation.errors));let c={preview:!0};j(i)||(c.validate=!1),E({attributes:t,context:n,clientId:r,query:c,delay:o}).done(({data:e})=>{if(!e)return void this.setHtml(`<div class="acf-block-fields acf-fields acf-empty-block-fields">${acf.__("Error previewing block")}</div>`);let t=e.preview.replaceAll(e.clientId,r);1==S(i)&&(t='<div class="acf-block-preview">'+t+"</div>"),acf.debug("fetch block render promise"),this.setHtml(acf.applyFilters("blocks/preview/render",t,!1)),e.validation&&this.setValidationErrors(e.validation.errors),this.isNotNewlyAdded()&&this.validate()})}validate(){const e=(acf.blockInstances[this.props.clientId]||{}).BlockForm||!1;e&&super.displayValidation(e.$el)}componentDidAppend(){super.componentDidAppend(),this.renderBlockPreviewEvent()}shouldComponentUpdate(e,t){const r=e.attributes,n=this.props.attributes;if(!R(r,n)||!R(e.context,this.props.context)){let t=0;r.className!==n.className&&(t=300),r.anchor!==n.anchor&&(t=300),acf.debug("Triggering fetch from block preview shouldComponentUpdate"),this.fetch({attributes:r,context:e.context,delay:t})}return super.shouldComponentUpdate(e,t)}renderBlockPreviewEvent(){const{attributes:e,name:t}=this.props,{$el:n,ref:o}=this.state;var i;const s=e.name.replace("acf/","");i=o&&o.current?r(o.current).parent():1==S(t)?n:n.parents(".acf-block-preview"),acf.doAction("render_block_preview",i,e),acf.doAction(`render_block_preview/type=${s}`,i,e)}componentDidRemount(){super.componentDidRemount(),acf.debug("Checking if fetch is required in BlockPreview componentDidRemount",Object.assign({},this.state.prevAttributes),Object.assign({},this.props.attributes),Object.assign({},this.state.prevContext),Object.assign({},this.props.context)),R(this.state.prevAttributes,this.props.attributes)&&R(this.state.prevContext,this.props.context)||(acf.debug("Triggering block preview fetch from componentDidRemount"),this.fetch()),this.renderBlockPreviewEvent()}}function W(e){return["top","center","bottom"].includes(e)?e:"top"}function X(e){const t=acf.get("rtl")?"right":"left";return["left","center","right"].includes(e)?e:t}function J(e){if(e){const[t,r]=e.split(" ");return`${W(t)} ${X(r)}`}return"center center"}function Y(e,t,r){return e[t]={type:r},e}function Q(e,r){return e._acf_context=G(r),t(JSON.stringify(G(e)))}function G(e){return Object.keys(e).sort().reduce((t,r)=>(t[r]=e[r],t),{})}acf.addAction("prepare",function(){wp.blockEditor||(wp.blockEditor=wp.editor);const e=acf.get("blockTypes");e&&e.map(C)})})(jQuery)})()})();