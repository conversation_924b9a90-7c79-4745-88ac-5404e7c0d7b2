<?php

add_action('acf/include_fields', function () {

    
    $title = 'Before - After';
    $titleImgBefore = 'Before Image';
    $titleImgAfter = 'After Image';
    $titleStartPosition = 'Separator Start Position';

    if(ADMIN_LOCALE == 'ru_RU'){
        $title = 'До - После';
        $titleImgBefore = 'Изображение до';
        $titleImgAfter = 'Изображение после';
        $titleStartPosition = 'Позиция разделителя';
    }

    acf_add_local_field_group([
        'key' => 'before_after',
        'title' => $title,
        'fields' => [
            [
                'key' => 'field_before_after_group',
                'label' => $title,
                'name' => 'before_after_group',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => [
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ],
                'layout' => 'block',
                'sub_fields' => [
                    [
                        'key' => 'field_before_after_img_before',
                        'label' => $titleImgBefore,
                        'name' => 'before_after_img_before',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'medium',
                    ],
                    [
                        'key' => 'field_before_after_img_after',
                        'label' => $titleImgAfter,
                        'name' => 'before_after_img_after',
                        'aria-label' => '',
                        'type' => 'image',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '50%',
                            'class' => '',
                            'id' => '',
                        ],
                        'return_format' => 'id',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => 'jpg,jpeg,png,svg',
                        'allow_in_bindings' => 0,
                        'preview_size' => 'medium',
                    ],
                    [
                        'key' => 'field_before_after_position',
                        'label' => $titleStartPosition,
                        'name' => 'before_after_position',
                        'aria-label' => '',
                        'type' => 'range',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => [
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ],
                        'default_value' => 50,
                        'min' => 0,
                        'max' => 100,
                        'allow_in_bindings' => 1,
                        'step' => 1,
                        'prepend' => '',
                        'append' => '%',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/before-after',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ]);
});